using OnePuz.Services;

namespace OnePuz.Ads
{
    public interface IAdController
    {
        bool IsInitialized { get; set; }
        void Init(IAdService manager, BaseAdUnitDefinition definitions);
        void RequestBanner(System.Action<bool> onLoaded);
        bool IsBannerLoaded();
        void ShowBanner();
        void HideBanner();
        void DestroyBanner();
        void StartBannerAutoRefresh();
        void StopBannerAutoRefresh();

        void RequestInterstitial(System.Action<bool> onLoaded);
        bool IsInterstitialLoaded();
        void ShowInterstitial();

        void RequestRewardedVideo(System.Action<bool> onLoaded);
        bool IsRewardedVideoLoaded();
        void ShowRewardedVideo();

        void RequestAppOpenAd(System.Action<bool> onLoaded);
        bool IsAppOpenAdLoaded();
        void ShowAppOpenAd();
        
        void OnApplicationPause(bool isPaused);
        void Dispose();
    }
}
