using System.Collections.Generic;
using UnityEngine;
using Unity.Collections;

namespace OP.BlockSand.Tests
{
    /// <summary>
    /// Test class for validating ShapeSpawnRule logic improvements
    /// </summary>
    public static class ShapeSpawnRuleTests
    {
        /// <summary>
        /// Run all basic tests for the new spawn logic
        /// </summary>
        public static void RunAllTests()
        {
            OLogger.Log("=== Starting ShapeSpawnRule Tests ===");
            
            TestBatchSpawnLogic();
            TestGuaranteedClearLogic();
            TestTrickyRoundLogic();
            TestThrottlingLogic();
            TestRandomPositioning();
            
            OLogger.Log("=== ShapeSpawnRule Tests Completed ===");
        }

        /// <summary>
        /// Test batch spawn logic for 3 shapes
        /// </summary>
        private static void TestBatchSpawnLogic()
        {
            OLogger.Log("Testing batch spawn logic...");
            
            // Create mock material definition
            var materialDefinition = CreateMockMaterialDefinition();
            var spawnRule = new ShapeSpawnRule(materialDefinition, 100); // Board width = 100
            
            // Test normal batch spawning
            var results = spawnRule.GenerateBatchShapeBlockTypes(4, 3);
            
            if (results.Count == 3)
            {
                OLogger.Log("✓ Batch spawn returns correct number of shapes");
            }
            else
            {
                OLogger.LogError($"✗ Batch spawn returned {results.Count} shapes, expected 3");
            }
            
            // Verify all results have valid block types
            var allValid = true;
            for (var i = 0; i < results.Count; i++)
            {
                if (results[i].blockTypes == null || results[i].blockTypes.Count != 4)
                {
                    allValid = false;
                    break;
                }
            }
            
            if (allValid)
            {
                OLogger.Log("✓ All batch spawn results have valid block types");
            }
            else
            {
                OLogger.LogError("✗ Some batch spawn results have invalid block types");
            }
        }

        /// <summary>
        /// Test GuaranteedClear logic with surface count comparison
        /// </summary>
        private static void TestGuaranteedClearLogic()
        {
            OLogger.Log("Testing GuaranteedClear logic...");
            
            var materialDefinition = CreateMockMaterialDefinition();
            var spawnRule = new ShapeSpawnRule(materialDefinition, 100); // Board width = 100
            
            // Set up spawn state for guaranteed clear
            spawnRule.OnSpawnAllShapes();
            spawnRule.OnSpawnAllShapes();
            spawnRule.OnSpawnAllShapes();
            spawnRule.OnSpawnAllShapes(); // This should trigger guaranteed clear
            
            // Create mock analysis data with low surface count (< board width)
            var mockAnalysisData = CreateMockAnalysisData(50); // Surface count = 50 < board width = 100
            spawnRule.UpdateMapAnalysis(mockAnalysisData, 100);
            
            // Test if it should spawn two same color shapes
            var shouldSpawnTwo = spawnRule.ShouldSpawnTwoSameColorShapes();
            if (shouldSpawnTwo)
            {
                OLogger.Log("✓ Correctly identified need for two same color shapes (low surface count)");
            }
            else
            {
                OLogger.LogError("✗ Failed to identify need for two same color shapes");
            }
            
            // Test batch spawn with guaranteed clear
            var results = spawnRule.GenerateBatchShapeBlockTypes(4, 3);
            var guaranteedClearCount = 0;
            
            foreach (var result in results)
            {
                if (result.isGuaranteedClear)
                {
                    guaranteedClearCount++;
                }
            }
            
            if (guaranteedClearCount >= 1)
            {
                OLogger.Log($"✓ Batch spawn contains {guaranteedClearCount} guaranteed clear shapes");
            }
            else
            {
                OLogger.LogError("✗ Batch spawn should contain at least 1 guaranteed clear shape");
            }
            
            // Clean up
            mockAnalysisData.Dispose();
        }

        /// <summary>
        /// Test Tricky round logic
        /// </summary>
        private static void TestTrickyRoundLogic()
        {
            OLogger.Log("Testing Tricky round logic...");
            
            var materialDefinition = CreateMockMaterialDefinition();
            var spawnRule = new ShapeSpawnRule(materialDefinition, 100);
            spawnRule.SetLevel(3); // Enable tricky rounds at level 3+
            
            // Trigger tricky round (every 5 spawn cycles at level 3+)
            for (var i = 0; i < 5; i++)
            {
                spawnRule.OnSpawnAllShapes();
            }
            
            // Create mock analysis data
            var mockAnalysisData = CreateMockAnalysisData(150);
            spawnRule.UpdateMapAnalysis(mockAnalysisData, 100);
            
            var results = spawnRule.GenerateBatchShapeBlockTypes(4, 3);
            var trickyRoundCount = 0;
            
            foreach (var result in results)
            {
                if (result.isTrickyRound)
                {
                    trickyRoundCount++;
                }
            }
            
            if (trickyRoundCount >= 1)
            {
                OLogger.Log($"✓ Batch spawn contains {trickyRoundCount} tricky round shapes");
            }
            else
            {
                OLogger.LogError("✗ Batch spawn should contain at least 1 tricky round shape");
            }
            
            // Clean up
            mockAnalysisData.Dispose();
        }

        /// <summary>
        /// Test throttling logic for map analysis updates
        /// </summary>
        private static void TestThrottlingLogic()
        {
            OLogger.Log("Testing throttling logic...");
            
            var materialDefinition = CreateMockMaterialDefinition();
            var spawnRule = new ShapeSpawnRule(materialDefinition, 100);
            
            var mockAnalysisData1 = CreateMockAnalysisData(100);
            var mockAnalysisData2 = CreateMockAnalysisData(105); // Small change
            
            // First update should go through
            spawnRule.UpdateMapAnalysis(mockAnalysisData1, 100);
            
            // Second update immediately after should be throttled
            spawnRule.UpdateMapAnalysis(mockAnalysisData2, 100);
            
            OLogger.Log("✓ Throttling logic tested (check logs for throttling messages)");
            
            // Clean up
            mockAnalysisData1.Dispose();
            mockAnalysisData2.Dispose();
        }

        /// <summary>
        /// Test random positioning of special shapes
        /// </summary>
        private static void TestRandomPositioning()
        {
            OLogger.Log("Testing random positioning...");
            
            var materialDefinition = CreateMockMaterialDefinition();
            var spawnRule = new ShapeSpawnRule(materialDefinition, 100);
            
            // Set up for guaranteed clear
            for (var i = 0; i < 4; i++)
            {
                spawnRule.OnSpawnAllShapes();
            }
            
            var mockAnalysisData = CreateMockAnalysisData(50);
            spawnRule.UpdateMapAnalysis(mockAnalysisData, 100);
            
            // Test multiple batches to see if positioning varies
            var positionCounts = new int[3]; // Track positions of special shapes
            
            for (var batch = 0; batch < 10; batch++)
            {
                var results = spawnRule.GenerateBatchShapeBlockTypes(4, 3);
                
                for (var i = 0; i < results.Count; i++)
                {
                    if (results[i].isGuaranteedClear)
                    {
                        positionCounts[i]++;
                    }
                }
                
                // Reset for next batch
                for (var i = 0; i < 4; i++)
                {
                    spawnRule.OnSpawnAllShapes();
                }
            }
            
            var hasVariation = positionCounts[0] != 10 || positionCounts[1] != 0 || positionCounts[2] != 0;
            if (hasVariation)
            {
                OLogger.Log($"✓ Random positioning working - Position counts: [{positionCounts[0]}, {positionCounts[1]}, {positionCounts[2]}]");
            }
            else
            {
                OLogger.LogWarning("? Random positioning may not be working as expected");
            }
            
            // Clean up
            mockAnalysisData.Dispose();
        }

        /// <summary>
        /// Create a mock PixelMaterials for testing
        /// </summary>
        private static PixelMaterials CreateMockMaterialDefinition()
        {
            // This would need to be implemented based on your PixelMaterials structure
            // For now, return null and handle in the actual implementation
            return null;
        }

        /// <summary>
        /// Create mock analysis data for testing
        /// </summary>
        private static MapAnalysisJobData CreateMockAnalysisData(int surfaceCount)
        {
            var data = MapAnalysisJobData.Create(10);
            
            // Set some mock values
            if (data.materialSurfacePixelCounts.IsCreated && data.materialSurfacePixelCounts.Length > 0)
            {
                data.materialSurfacePixelCounts[0] = surfaceCount;
            }
            
            if (data.totalNonEmptyPixels.IsCreated)
            {
                data.totalNonEmptyPixels[0] = surfaceCount * 2;
            }
            
            return data;
        }
    }
}
