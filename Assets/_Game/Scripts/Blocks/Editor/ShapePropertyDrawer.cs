using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities.Editor;
using OP.BlockSand;

namespace OP.BlockSand.Editor
{
    [CustomPropertyDrawer(typeof(ShapeDefinition.Datum))]
    public class ShapePropertyDrawer : PropertyDrawer
    {
        private const int GRID_SIZE = 5;
        private static Dictionary<string, bool> _foldoutStates = new Dictionary<string, bool>();
        private static Dictionary<string, bool[,]> _gridStates = new Dictionary<string, bool[,]>();
        private static Dictionary<string, int> _coordinatesHashes = new Dictionary<string, int>();

        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);

            var idProperty = property.FindPropertyRelative("id");
            var enableMultiColorProperty = property.FindPropertyRelative("enableMultiColor");
            var sizeProperty = property.FindPropertyRelative("size");
            var coordinatesProperty = property.FindPropertyRelative("coordinates");
            var propertyPath = property.propertyPath;

            // Initialize states if needed
            if (!_foldoutStates.ContainsKey(propertyPath))
                _foldoutStates[propertyPath] = true;
            
            if (!_gridStates.ContainsKey(propertyPath))
                _gridStates[propertyPath] = new bool[GRID_SIZE, GRID_SIZE];

            // Check if coordinates changed externally and update grid
            var currentHash = GetCoordinatesHash(coordinatesProperty);
            if (!_coordinatesHashes.ContainsKey(propertyPath) || _coordinatesHashes[propertyPath] != currentHash)
            {
                UpdateGridFromCoordinates(coordinatesProperty, propertyPath);
                _coordinatesHashes[propertyPath] = currentHash;
            }

            var rect = position;
            rect.height = EditorGUIUtility.singleLineHeight;

            // Draw ID field
            EditorGUI.PropertyField(rect, idProperty);
            rect.y += EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing;
            
            // Draw enable multi-color field
            EditorGUI.PropertyField(rect, enableMultiColorProperty);
            rect.y += EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing;
            
            // Draw size field
            EditorGUI.PropertyField(rect, sizeProperty);
            rect.y += EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing;
            
            // Update size from coordinates
            UpdateSizeFromCoordinates(sizeProperty, coordinatesProperty);

            // Draw foldout for grid
            _foldoutStates[propertyPath] = EditorGUI.Foldout(rect, _foldoutStates[propertyPath], "Shape Grid (5x5)", true);
            rect.y += EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing;

            if (_foldoutStates[propertyPath])
            {
                // Draw coordinate info
                DrawCoordinateInfo(rect, coordinatesProperty);
                rect.y += EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing;

                // Draw grid
                DrawGrid(rect, coordinatesProperty, propertyPath);
                rect.y += GetGridHeight() + EditorGUIUtility.standardVerticalSpacing;

                // Draw utility buttons
                DrawUtilityButtons(rect, coordinatesProperty, propertyPath);
                rect.y += EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing;
            }

            // Draw coordinates list (read-only for reference)
            using (new EditorGUI.DisabledScope(true))
            {
                EditorGUI.PropertyField(rect, coordinatesProperty, new GUIContent("Coordinates (Read-only)"), true);
            }

            EditorGUI.EndProperty();
        }

        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            var propertyPath = property.propertyPath;
            var height = EditorGUIUtility.singleLineHeight * 5 + EditorGUIUtility.standardVerticalSpacing; // ID + foldout

            if (_foldoutStates.ContainsKey(propertyPath) && _foldoutStates[propertyPath])
            {
                height += EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing; // Coordinate info
                height += GetGridHeight() + EditorGUIUtility.standardVerticalSpacing; // Grid
                height += EditorGUIUtility.singleLineHeight + EditorGUIUtility.standardVerticalSpacing; // Utility buttons
            }

            // Coordinates list height
            var coordinatesProperty = property.FindPropertyRelative("coordinates");
            height += EditorGUI.GetPropertyHeight(coordinatesProperty, true) + EditorGUIUtility.standardVerticalSpacing;

            return height;
        }

        private int GetCoordinatesHash(SerializedProperty coordinatesProperty)
        {
            var hash = 0;
            for (int i = 0; i < coordinatesProperty.arraySize; i++)
            {
                var element = coordinatesProperty.GetArrayElementAtIndex(i);
                var x = element.FindPropertyRelative("x").intValue;
                var y = element.FindPropertyRelative("y").intValue;
                hash = hash * 31 + (x * 1000 + y);
            }
            return hash;
        }

        private void UpdateGridFromCoordinates(SerializedProperty coordinatesProperty, string propertyPath)
        {
            // Clear grid
            _gridStates[propertyPath] = new bool[GRID_SIZE, GRID_SIZE];

            // Set grid state from coordinates
            for (int i = 0; i < coordinatesProperty.arraySize; i++)
            {
                var coordElement = coordinatesProperty.GetArrayElementAtIndex(i);
                var x = coordElement.FindPropertyRelative("x").intValue;
                var y = coordElement.FindPropertyRelative("y").intValue;

                // Convert to grid coordinates (coordinates are from bottom-left, grid is top-left)
                var gridX = x;
                var gridY = y;

                if (gridX >= 0 && gridX < GRID_SIZE && gridY >= 0 && gridY < GRID_SIZE)
                {
                    _gridStates[propertyPath][gridX, gridY] = true;
                }
            }
        }

        private void DrawCoordinateInfo(Rect position, SerializedProperty coordinatesProperty)
        {
            var count = coordinatesProperty.arraySize;
            EditorGUI.LabelField(position, $"Active Blocks: {count}", EditorStyles.helpBox);
        }

        private void DrawGrid(Rect position, SerializedProperty coordinatesProperty, string propertyPath)
        {
            var buttonSize = 30f;
            var spacing = 2f;
            var startX = position.x;
            var startY = position.y;

            EditorGUI.BeginChangeCheck();

            // Draw grid labels
            var labelStyle = new GUIStyle(EditorStyles.miniLabel) { alignment = TextAnchor.MiddleCenter };
            
            // Y labels (left side)
            for (int y = GRID_SIZE - 1; y >= 0; y--)
            {
                var labelRect = new Rect(startX - 20, startY + (GRID_SIZE - 1 - y) * (buttonSize + spacing), 15, buttonSize);
                EditorGUI.LabelField(labelRect, y.ToString(), labelStyle);
            }

            // X labels (bottom)
            for (int x = 0; x < GRID_SIZE; x++)
            {
                var labelRect = new Rect(startX + x * (buttonSize + spacing), startY + GRID_SIZE * (buttonSize + spacing), buttonSize, 15);
                EditorGUI.LabelField(labelRect, x.ToString(), labelStyle);
            }

            // Draw grid from top to bottom (y = 4 to 0)
            for (int y = GRID_SIZE - 1; y >= 0; y--)
            {
                for (int x = 0; x < GRID_SIZE; x++)
                {
                    var buttonRect = new Rect(
                        startX + x * (buttonSize + spacing),
                        startY + (GRID_SIZE - 1 - y) * (buttonSize + spacing),
                        buttonSize,
                        buttonSize
                    );

                    var oldColor = GUI.backgroundColor;
                    GUI.backgroundColor = _gridStates[propertyPath][x, y] ? Color.green : Color.white;

                    var newState = GUI.Toggle(buttonRect, _gridStates[propertyPath][x, y], "", "Button");
                    _gridStates[propertyPath][x, y] = newState;

                    GUI.backgroundColor = oldColor;
                }
            }

            if (EditorGUI.EndChangeCheck())
            {
                UpdateCoordinatesFromGrid(coordinatesProperty, propertyPath);
            }
        }

        private void UpdateCoordinatesFromGrid(SerializedProperty coordinatesProperty, string propertyPath)
        {
            var coordinates = new List<Vector2Int>();

            // Convert grid state to coordinates (grid y=0 is bottom, grid y=4 is top)
            for (int x = 0; x < GRID_SIZE; x++)
            {
                for (int y = 0; y < GRID_SIZE; y++)
                {
                    if (_gridStates[propertyPath][x, y])
                    {
                        // Convert grid coordinates to world coordinates
                        coordinates.Add(new Vector2Int(x, y));
                    }
                }
            }

            // Sort coordinates for consistency
            coordinates.Sort((a, b) => a.x != b.x ? a.x.CompareTo(b.x) : a.y.CompareTo(b.y));

            // Update SerializedProperty
            coordinatesProperty.ClearArray();
            for (int i = 0; i < coordinates.Count; i++)
            {
                coordinatesProperty.InsertArrayElementAtIndex(i);
                var element = coordinatesProperty.GetArrayElementAtIndex(i);
                element.FindPropertyRelative("x").intValue = coordinates[i].x;
                element.FindPropertyRelative("y").intValue = coordinates[i].y;
            }

            coordinatesProperty.serializedObject.ApplyModifiedProperties();
            _coordinatesHashes[propertyPath] = GetCoordinatesHash(coordinatesProperty);
        }

        private void UpdateSizeFromCoordinates(SerializedProperty sizeProperty, SerializedProperty coordinatesProperty)
        {
            var minX = int.MaxValue;
            var maxX = int.MinValue;
            var minY = int.MaxValue;
            var maxY = int.MinValue;

            for (int i = 0; i < coordinatesProperty.arraySize; i++)
            {
                var coordElement = coordinatesProperty.GetArrayElementAtIndex(i);
                var x = coordElement.FindPropertyRelative("x").intValue;
                var y = coordElement.FindPropertyRelative("y").intValue;

                minX = Mathf.Min(minX, x);
                maxX = Mathf.Max(maxX, x);
                minY = Mathf.Min(minY, y);
                maxY = Mathf.Max(maxY, y);
            }

            sizeProperty.vector2IntValue = new Vector2Int(maxX - minX + 1, maxY - minY + 1);
        }

        private void DrawUtilityButtons(Rect position, SerializedProperty coordinatesProperty, string propertyPath)
        {
            var buttonWidth = 80f;
            var spacing = 5f;

            var clearRect = new Rect(position.x, position.y, buttonWidth, EditorGUIUtility.singleLineHeight);
            var fillRect = new Rect(position.x + buttonWidth + spacing, position.y, buttonWidth, EditorGUIUtility.singleLineHeight);
            var presetRect = new Rect(position.x + (buttonWidth + spacing) * 2, position.y, buttonWidth, EditorGUIUtility.singleLineHeight);

            if (GUI.Button(clearRect, "Clear All"))
            {
                _gridStates[propertyPath] = new bool[GRID_SIZE, GRID_SIZE];
                UpdateCoordinatesFromGrid(coordinatesProperty, propertyPath);
            }

            if (GUI.Button(fillRect, "Fill All"))
            {
                for (int x = 0; x < GRID_SIZE; x++)
                {
                    for (int y = 0; y < GRID_SIZE; y++)
                    {
                        _gridStates[propertyPath][x, y] = true;
                    }
                }
                UpdateCoordinatesFromGrid(coordinatesProperty, propertyPath);
            }

            if (GUI.Button(presetRect, "T-Shape"))
            {
                _gridStates[propertyPath] = new bool[GRID_SIZE, GRID_SIZE];
                // Create T-shape pattern
                _gridStates[propertyPath][1, 1] = true; // (1,3)
                _gridStates[propertyPath][2, 1] = true; // (2,3)
                _gridStates[propertyPath][3, 1] = true; // (3,3)
                _gridStates[propertyPath][2, 2] = true; // (2,2)
                UpdateCoordinatesFromGrid(coordinatesProperty, propertyPath);
            }
        }

        private float GetGridHeight()
        {
            var buttonSize = 30f;
            var spacing = 2f;
            return GRID_SIZE * (buttonSize + spacing) + 20f; // Extra space for labels
        }
    }
}
