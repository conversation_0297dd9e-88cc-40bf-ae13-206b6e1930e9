using System.Collections.Generic;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockSand
{
    [CreateAssetMenu(fileName = "ShapeDefinitions.asset", menuName = "Game/ShapeDefinitions")]
    public class ShapeDefinition : ScriptableObject
    {
        public int defaultBlockCount = 4;
        public GameObject shapePrefab;
        
        public List<Datum> shapes;
        
        public Datum GetRandomShape(bool enableMultiColor = true)
        {
            var rnd = Random.Range(0, shapes.Count);
            while (!shapes[rnd].enableMultiColor && enableMultiColor)
            {
                rnd = Random.Range(0, shapes.Count);
            }

            return shapes[rnd];
        }
        
        [System.Serializable]
        public class Datum
        {
            [Title("Shape Configuration")]
            [LabelWidth(60)]
            public string id;

            [Space(5)]
            [ReadOnly]
            public Vector2Int size;
            
            public bool enableMultiColor = true;

            [Space(5)]
            [ReadOnly]
            [ListDrawerSettings(ShowIndexLabels = false, DraggableItems = false, ShowPaging = false)]
            public List<Vector2Int> coordinates;
        }
    }
}