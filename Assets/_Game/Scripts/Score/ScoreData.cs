using System.Collections.Generic;
using OnePuz.Data;
using OnePuz.Data.Services;

namespace OP.BlockSand
{
    public class ScoreData : ISaveData, IVersionedData
    {
        [System.Serializable]
        public class Config
        {
            public float basePointPerPixel;
            public float bonusPointPerPixel;
            public int consecutivePlacedShapesToResetCombo;
            public List<long> targetScoreForLevelUp;
            public long loopAdditionalTargetScore;
            
            public int GetLevelByScore(long score)
            {
                if (targetScoreForLevelUp[^1] <= score)
                {
                    return (int)((score - targetScoreForLevelUp[^1]) / loopAdditionalTargetScore + targetScoreForLevelUp.Count);
                }
                
                for (var i = 0; i < targetScoreForLevelUp.Count; i++)
                {
                    if (score < targetScoreForLevelUp[i])
                        return i;
                }
                
                return targetScoreForLevelUp.Count;
            }

            public (long, long) GetScoreRangeForLevel(int level)
            {
                if (level < targetScoreForLevelUp.Count)
                {
                    return level == 0 ? (0, targetScoreForLevelUp[0]) : (targetScoreForLevelUp[level - 1], targetScoreForLevelUp[level]);
                }
                
                var startScore = targetScoreForLevelUp[^1] + (level - targetScoreForLevelUp.Count) * loopAdditionalTargetScore;
                return (startScore, startScore + loopAdditionalTargetScore);
            }
        }

        public int level;
        public int currentCombo;
        public long currentScore;
        public long highScore;
        public int consecutivePlacedShapes;
        
        [RemoteValue("score_config")]
        public Config config;
        
        public override void SetupDefaultValues()
        {
            currentCombo = 0;
            currentScore = 0;
            highScore = 0;
            consecutivePlacedShapes = 0;
            
            config = new Config
            {
                basePointPerPixel = 0.1f,
                bonusPointPerPixel = 0.35f,
                consecutivePlacedShapesToResetCombo = 3,
                targetScoreForLevelUp = new List<long> { 10000, 25000, 50000 },
                loopAdditionalTargetScore = 25000
            };
        }
        
        public override void ManualSave() { }
        
        public void AddScore(int score)
        {
            var lastScore = currentScore;
            currentScore += score;
            
            var isHighScore = false;
            if (currentScore > highScore)
            {
                highScore = currentScore;
                isHighScore = true;
            }
            
            var updateLevel = config.GetLevelByScore(currentScore);
            if (updateLevel > level)
            {
                level = updateLevel;
                Core.Event.Fire(new OnScoreChangedEvent() { lastScore = lastScore, currentScore = currentScore, isLevelUp = true, isHighScore = isHighScore});
                Core.Event.Fire(new OnScoreLevelUpEvent() { level = level });
                return;
            }
            
            Core.Event.Fire(new OnScoreChangedEvent() { lastScore = lastScore, currentScore = currentScore, isLevelUp = false, isHighScore = isHighScore});
        }
        
        public void ResetScore()
        {
            var lastScore = currentScore;
            currentScore = 0;
            level = 0;
            
            Core.Event.Fire(new OnScoreChangedEvent() { lastScore = lastScore, currentScore = currentScore });
        }
        
        public void AddCombo()
        {
            currentCombo++;
            consecutivePlacedShapes = 0;
            Core.Event.Fire(new OnGotComboEvent() { combo = currentCombo });
        }
        
        public void ResetCombo()
        {
            currentCombo = 0;
        }
        
        public void PlacedShape()
        {
            consecutivePlacedShapes++;
            if (consecutivePlacedShapes <= config.consecutivePlacedShapesToResetCombo) return;
            consecutivePlacedShapes = 0;
            ResetCombo();
        }
        
        public int Version { get; set; }
        public void Migrate()
        {
            
        }
    }
}