using System.Collections.Generic;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Services;
using OnePuz.Services.Extensions;
using UnityEngine;

namespace OP.BlockSand
{
    public struct OnScoreChangedEvent
    {
        public long lastScore;
        public long currentScore;
        public bool isLevelUp;
        public bool isHighScore;
    }

    public struct OnScoreLevelUpEvent
    {
        public int level;
    }
    
    public struct OnGotComboEvent
    {
        public int combo;
    }

    public class ScoreSystem : IServiceLoad, IServiceUnload
    {
        private ScoreDefinition _definition;
        private ScoreData _data;
        
        private readonly List<int> _randomVoiceIndices = new List<int> { 0, 1, 2 };
        private int _lastPlayedVoiceIndex = 0;

        public void Load()
        {
            this.EventSubscribe<OnPlacedShapeEvent>(HandlePlacedShape);
            this.EventSubscribe<OnClearSandClusterEvent>(HandleClearSandCluster);

            _definition = Core.Definition.Score;
            _data = DataShortcut.Score;
        }

        private int CalculateBonusScore(int pixelCount, int currentCombo)
        {
            return (int)(pixelCount * CalculateComboMultiplier(currentCombo) * _data.config.bonusPointPerPixel);
        }

        private int CalculateBaseScore(int pixelCount)
        {
            return (int)(pixelCount * _data.config.basePointPerPixel) + Random.Range(0, 5);
        }

        private float CalculateComboMultiplier(int currentCombo)
        {
            return 1f + Mathf.Clamp(currentCombo, 0, 5) * 0.1f;
        }

        private void HandleClearSandCluster(OnClearSandClusterEvent e)
        {
            var floatingScore = Core.ScenePool.Spawn(_definition.floatingScorePrefab).GetComponent<FloatingScore>();
            var score = CalculateBonusScore(e.pixelCount, _data.currentCombo);
            floatingScore.transform.position = e.worldPosition + new Vector3(Random.Range(0f, 0.0f), Random.Range(1.5f, 2.0f), 0);
            floatingScore.transform.localScale = Vector3.one * 1f;
            floatingScore.Show(score, false, 0, GetVoiceIndex(e.pixelCount, _data.currentCombo));
            
            AudioShortcut.PlaySandMatch(_data.currentCombo);
            Core.Vibration.VibrateMedium();

            _data.AddScore(score);
            _data.AddCombo();
        }

        private int GetVoiceIndex(long pixelCount, int currentCombo)
        {
            if (pixelCount <= 8 * 8 * 4 * 4 && currentCombo <= 2) return -1;
            
            _randomVoiceIndices.Remove(_lastPlayedVoiceIndex);
            _randomVoiceIndices.Insert(0, _lastPlayedVoiceIndex);
            _lastPlayedVoiceIndex = _randomVoiceIndices[Random.Range(1, _randomVoiceIndices.Count)];
            return _lastPlayedVoiceIndex;

        }

        private void HandlePlacedShape(OnPlacedShapeEvent e)
        {
            var floatingScore = Core.ScenePool.Spawn(_definition.floatingScorePrefab).GetComponent<FloatingScore>();
            var score = CalculateBaseScore(e.pixelCount);
            floatingScore.transform.localScale = Vector3.one;
            floatingScore.transform.position = e.worldPosition + Vector3.up * 0f;
            floatingScore.Show(score, true, 0, -1);

            _data.AddScore(score);
            _data.PlacedShape();
        }

        public void Reset()
        {
            _data.ResetScore();
            _data.ResetCombo();
        }

        public void Unload()
        {
            this.EventUnsubscribe<OnPlacedShapeEvent>(HandlePlacedShape);
            this.EventUnsubscribe<OnClearSandClusterEvent>(HandleClearSandCluster);
        }
    }
}