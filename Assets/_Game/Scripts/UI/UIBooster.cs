using _FeatureHub.Attributes.Core;
using AssetKits.ParticleImage;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using Sirenix.OdinInspector;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    [ReferenceInBackground]
    public class UIBooster : Mono<PERSON><PERSON><PERSON><PERSON>, IRewardTarget
    {
        [SerializeField, ReferenceValue]
        public Button _clickHandler;
        
        [SerializeField, ReferenceValue]
        private Animator _animator;

        [SerializeField]
        private AnimationClip _collectedAnimation;
        
        [FoldoutGroup("Activated", Expanded = true)]
        [SerializeField, ReferenceValue("PanelActivated")]
        private Transform m_PanelActivated;

        [FoldoutGroup("Activated")]
        [SerializeField, ReferenceValue("@m_PanelActivated/CollectVFX")]
        private ParticleImage _collectVFX;

        [FoldoutGroup("Activated")]
        [SerializeField, ReferenceValue("@m_PanelActivated/PanelQuantity")]
        private GameObject _panelQuantity;
        
        [FoldoutGroup("Activated")]
        [SerializeField, ReferenceValue("@_panelQuantity/LabelQuantity")]
        private TMP_Text _labelQuantity;

        [FoldoutGroup("Activated")]
        [SerializeField, ReferenceValue("@m_PanelActivated/PanelPrice")]
        private GameObject _panelPrice;
        
        [FoldoutGroup("Activated")]
        [SerializeField, ReferenceValue("@_panelPrice/Label")]
        private TMP_Text _labelPrice;
        
        [FoldoutGroup("Locked", Expanded = true)]
        [SerializeField, ReferenceValue("PanelLocked")]
        private Transform m_PanelLocked;

        [FoldoutGroup("Locked")]
        [SerializeField, ReferenceValue("@m_PanelLocked/Label")]
        private TMP_Text m_LabelLevelUnlock;
        
        internal bool _isLocked;
        internal BoosterType _boosterType;
        internal int _currentQuantity;
        internal BoosterDefinitionData _definition;
        
        public void Init(BoosterType boosterType)
        {
            _boosterType = boosterType;
            _definition = Core.Definition.Booster.GetDefinition(_boosterType);
            UpdateStatus();
        }

        public void UpdateStatus()
        {
            _currentQuantity = DataShortcut.Booster.GetBooster(_boosterType);
            _isLocked = DataShortcut.Level.Current < Core.Definition.Booster.GetDefinition(_boosterType).levelUnlock;
            UpdateUI();
        }
        
        private void UpdateUI()
        {
            if(_isLocked)
            {
                m_PanelLocked.SetActive(true);
                m_PanelActivated.SetActive(false);
                m_LabelLevelUnlock.text = $"Level {Core.Definition.Booster.GetDefinition(_boosterType).levelUnlock + 1}";
            }
            else
            {
                m_PanelLocked.SetActive(false);
                m_PanelActivated.SetActive(true);
                _panelPrice.gameObject.SetActive(_currentQuantity == 0);
                _labelPrice.text = _definition.price.value.ToString();
                _panelQuantity.SetActive(_currentQuantity > 0);
                _labelQuantity.text = _currentQuantity.ToString();
            }
        }

        public RectTransform GetRewardTargetTransform()
        {
            return GetComponent<RectTransform>();
        }

        public void OnRewardReachedTarget(int rewardAmount)
        {
            AudioShortcut.PlayCollectItem();
            _animator.PlayManual(Animator.StringToHash(_collectedAnimation.name));
            _collectVFX?.Play();
            
            _currentQuantity++;
            UpdateUI();
        }

        public void Appear()
        {
        }

        public void OnLastRewardReachedTarget()
        {
            UpdateStatus();
        }
    }
}