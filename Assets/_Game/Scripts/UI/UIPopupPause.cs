using System.Threading;
using _FeatureHub.Attributes.Core;
using _Main.Scripts.Common;
using Cysharp.Threading.Tasks;
using OnePuz.Ads;
using OnePuz.Data;
using OnePuz.Extensions;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupPause : UIBasePanel
    {
        private readonly int hashFadeIn = Animator.StringToHash("Popup_TransitionIn");

        [FormerlySerializedAs("m_Animator")]
        [SerializeField, ReferenceValue]
        private Animator _animator;

        [FormerlySerializedAs("m_BtnClose")]
        [SerializeField, ReferenceValue("Popup/ButtonClose")]
        private Button _btnClose;

        [FormerlySerializedAs("m_BtnReplay")]
        [SerializeField, ReferenceValue("Popup/ButtonReplay")]
        private Button _btnReplay;

        [FormerlySerializedAs("m_BtnPrivacy")]
        [SerializeField, ReferenceValue("Popup/ButtonHome")]
        private Button _btnHome;

        [FormerlySerializedAs("m_TglSound")]
        [SerializeField, ReferenceValue("PanelMiddle/ToggleSound")]
        private Toggle _toggleSound;

        [FormerlySerializedAs("m_TglVibration")]
        [SerializeField, ReferenceValue("PanelMiddle/ToggleHaptic")]
        private Toggle _toggleVibration;

        [FormerlySerializedAs("m_BtnPrivacy")]
        [SerializeField, ReferenceValue("Popup/ButtonTutorial")]
        private Button _btnTutorial;

        protected override UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            return UniTask.CompletedTask;
        }

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(hashFadeIn);
        }

        protected override void OnBeforeFocus()
        {
            _toggleSound.isOn = DataShortcut.Setting.SoundEnabled;
            _toggleVibration.isOn = DataShortcut.Setting.VibrationEnabled;

            _toggleSound.onValueChanged.AddListener(HandleSoundChanged);
            _toggleVibration.onValueChanged.AddListener(HandleVibrationChanged);
        }
        
        private void HandleSoundChanged(bool isOn)
        {
            Core.Audio.SetSoundState(isOn);
        }

        private void HandleVibrationChanged(bool isOn)
        {
            Core.Vibration.EnableVibration(isOn);
        }

        protected override void OnAfterFocus()
        {
            _btnClose.onClick.AddListener(CloseOnClick);
            _btnReplay.onClick.AddListener(ReplayOnClick);
            _btnHome.onClick.AddListener(HomeOnClick);
            _btnTutorial.onClick.AddListener(TutorialOnClick);
        }

        protected override void OnBeforeLostFocus()
        {
            _btnClose.onClick.RemoveListener(CloseOnClick);
            _btnReplay.onClick.RemoveListener(ReplayOnClick);
            _btnHome.onClick.RemoveListener(HomeOnClick);
            _btnTutorial.onClick.RemoveListener(TutorialOnClick);

            _toggleSound.onValueChanged.RemoveAllListeners();
            _toggleVibration.onValueChanged.RemoveAllListeners();
        }
        
        private void TutorialOnClick()
        {
            UIShortcut.ShowPanel(UIKeys.Panel.POPUP_TUTORIAL);
        }

        private void CloseOnClick() => Close();

        private void HomeOnClick()
        {
            BlockIndicator.Toast($"Coming soon!");
        }

        private void ReplayOnClick()
        {
            Core.Ads.ShowInterstitial();
            Close();
            Core.Live.LostLive();
            Core.Replay();
        }
    }
}