using System.Threading;
using _FeatureHub.Attributes.Core;
using _Main.Scripts.Common;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupLose : UIBasePanel
    {
        private readonly int hashFadeIn = Animator.StringToHash("Popup_TransitionIn");
        private readonly int hashFadeOut = Animator.StringToHash("Popup_TransitionOut");

        [SerializeField, ReferenceValue]
        private Animator _animator;

        [SerializeField, ReferenceValue("Popup/ButtonReviveAds")]
        private Button _btnReviveAds;

        [SerializeField, ReferenceValue("Popup/ButtonReviveGem")]
        private Button _btnReviveGem;

        [SerializeField, ReferenceValue("Popup/ButtonReviveGem/LabelPrice")]
        private TMP_Text _labelGemPrice;

        [Serial<PERSON><PERSON><PERSON>, ReferenceValue("Popup/ButtonReplay")]
        private Button _btnReplay;

        [<PERSON><PERSON><PERSON><PERSON><PERSON>, ReferenceValue("Popup/LabelScore")]
        private TMP_Text _labelScore;

        [SerializeField, ReferenceValue("Popup/LabelHighScore")]
        private TMP_Text _labelHighScore;

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(hashFadeIn);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(hashFadeOut);
        }

        protected override void OnBeforeFocus()
        {
            _labelScore.text = DataShortcut.Score.currentScore.ToFormattedString(3, true, 7);
            _labelHighScore.text = DataShortcut.Score.highScore.ToFormattedString();
            _labelGemPrice.text = "250";
        }

        protected override void OnAfterFocus()
        {
            _btnReviveAds.onClick.AddListener(ReviveAdsOnClick);
            _btnReviveGem.onClick.AddListener(ReviveGemOnClick);
            _btnReplay.onClick.AddListener(ReplayOnClick);
        }

        protected override void OnBeforeLostFocus()
        {
            _btnReviveAds.onClick.RemoveListener(ReviveAdsOnClick);
            _btnReviveGem.onClick.RemoveListener(ReviveGemOnClick);
            _btnReplay.onClick.RemoveListener(ReplayOnClick);
        }

        private void ReviveGemOnClick()
        {
            BlockIndicator.Toast($"Coming soon!");
        }

        private void ReviveAdsOnClick()
        {
            BlockIndicator.Toast($"Coming soon!");
        }

        private void ReplayOnClick()
        {
            if (Core.Live.CanPlay)
            {
                Core.Ads.ShowInterstitial();
                Close();
                Core.Live.LostLive();
                Core.Replay();
            }
            else
            {
                Core.Home();
            }
        }
    }
}