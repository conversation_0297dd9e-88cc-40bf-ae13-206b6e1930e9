using System.Threading;
using _FeatureHub.Attributes.Core;
using _Main.Scripts.Common;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using OP.BlockSand;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupUnlockColor : UIBasePanel
    {
        private readonly int hashFadeIn = Animator.StringToHash("Popup_TransitionIn");
        private readonly int hashFadeOut = Animator.StringToHash("Popup_TransitionOut");

        [SerializeField, ReferenceValue]
        private Animator _animator;

        [SerializeField, ReferenceValue("Popup/IconNewColor")]
        private Image _iconNewColor;

        [SerializeField, ReferenceValue("Popup/ButtonContinue")]
        private Button _btnContinue;
        
        [SerializeField]
        private BlockDefinitions _blockDefinition;

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(hashFadeIn);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(hashFadeOut);
        }

        protected override void OnBeforeFocus()
        {
            var unlockedBlockType = pArgs.GetData<BlockType>("unlockedBlockType");
            _iconNewColor.sprite = _blockDefinition.GetRandomTexture(unlockedBlockType);
        }

        protected override void OnAfterFocus()
        {
            _btnContinue.onClick.AddListener(ContinueOnClick);
        }

        protected override void OnBeforeLostFocus()
        {
            _btnContinue.onClick.RemoveListener(ContinueOnClick);
        }

        private void ContinueOnClick()
        {
            Close();
        }
    }
}