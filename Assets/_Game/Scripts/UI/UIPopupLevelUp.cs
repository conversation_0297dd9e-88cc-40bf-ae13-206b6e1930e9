using System.Threading;
using _FeatureHub.Attributes.Core;
using _Main.Scripts.Common;
using Coffee.UIExtensions;
using Cysharp.Threading.Tasks;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Extensions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace OnePuz.UI
{
    public class UIPopupLevelUp : UIBasePanel
    {
        private readonly int hashFadeIn = Animator.StringToHash("Popup_TransitionIn");
        private readonly int hashFadeOut = Animator.StringToHash("Popup_TransitionOut");

        [SerializeField, ReferenceValue]
        private Animator _animator;

        [Serial<PERSON><PERSON><PERSON>, ReferenceValue("Popup/ButtonClaimX3")]
        private Button _btnClaimX3;

        [SerializeField, ReferenceValue("Popup/ButtonNoThank")]
        private Button _btnNoThank;

        [SerializeField, ReferenceValue("Popup/LabelReward")]
        private TMP_Text _labelReward;

        [<PERSON><PERSON><PERSON><PERSON><PERSON>, ReferenceValue("Popup/LabelScore")]
        private TMP_Text _labelScore;

        [SerializeField]
        private UIParticle[] _confettiParticles;

        protected override async UniTask TransitionInAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(hashFadeIn);
        }

        protected override async UniTask TransitionOutAsync(CancellationToken cancellationToken)
        {
            await _animator.PlayManual(hashFadeOut);
        }

        protected override void OnBeforeFocus()
        {
            var (reachedScore, _) = DataShortcut.Score.config.GetScoreRangeForLevel(DataShortcut.Score.level);
            _labelScore.text = reachedScore.ToFormattedString(3, true, 7);
            _labelReward.text = "+5";
            
            PlayConfettiAsync(_cancellationToken).Forget();
        }

        protected override void OnAfterFocus()
        {
            _btnClaimX3.onClick.AddListener(ClaimX3OnClick);
            _btnNoThank.onClick.AddListener(NoThankOnClick);
        }

        protected override void OnBeforeLostFocus()
        {
            _btnClaimX3.onClick.RemoveListener(ClaimX3OnClick);
            _btnNoThank.onClick.RemoveListener(NoThankOnClick);
        }

        private void ClaimX3OnClick()
        {
            BlockIndicator.Toast($"Coming soon!");
        }

        private void NoThankOnClick()
        {
            Close();
        }

        private async UniTask PlayConfettiAsync(CancellationToken cancellationToken)
        {
            await UniTask.Delay(250, cancellationToken: cancellationToken);
            foreach (var confetti in _confettiParticles)
            {
                confetti.Play();
                AudioShortcut.PlayConfetti();
                await UniTask.Delay(250, cancellationToken: cancellationToken);
            }
        }
    }
}