using Newtonsoft.Json;
using UnityEngine;

namespace OnePuz.Data
{
    [System.Serializable]
    public class SettingData : ISaveData, IVersionedData
    {
        [JsonIgnore]
        public bool SoundEnabled
        {
            get => _soundEnabled;
            set => _soundEnabled = value;
        }

        [JsonIgnore]
        public bool MusicEnabled
        {
            get => _musicEnabled;
            set => _musicEnabled = value;
        }

        [JsonIgnore]
        public bool VibrationEnabled
        {
            get => _vibrationEnabled;
            set => _vibrationEnabled = value;
        }

        [JsonProperty]
        private bool _soundEnabled;
        [JsonProperty]
        private bool _musicEnabled;
        [JsonProperty]
        private bool _vibrationEnabled;

        public override void SetupDefaultValues()
        {
            _soundEnabled = true;
            _musicEnabled = true;
            _vibrationEnabled = true;
        }
        
        public override void ManualSave() { }
        
        public int Version { get; set; } = 0;
        
        public void Migrate()
        {
            if (Version < 1)
            {
            }
        }
    }
}