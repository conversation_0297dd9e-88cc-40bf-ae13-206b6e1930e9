using System.Collections.Generic;
using Newtonsoft.Json;
using OnePuz.Data.Services;

namespace OnePuz.Data
{
    [System.Serializable]
    public class RatingData : ISaveData, IVersionedData
    {
        [JsonIgnore]
        public readonly List<int> ratingLevelList = new List<int> { 6 - 1, 21 - 1, 31 - 1 };

        [JsonProperty, RemoteValue("rating_popup")]
        public bool enableRatingPopup;

        [JsonProperty]
        public bool alreadyRated;

        [JsonProperty]
        public List<int> alreadyShownRatingLevels = new();

        public override void SetupDefaultValues()
        {
            enableRatingPopup = true;
            alreadyRated = false;

            alreadyShownRatingLevels = new List<int>();
        }
        
        public override void ManualSave() { }

        public int Version { get; set; } = 0;

        public void Migrate()
        {
            if (Version < 1)
            {
            }
        }
    }
}