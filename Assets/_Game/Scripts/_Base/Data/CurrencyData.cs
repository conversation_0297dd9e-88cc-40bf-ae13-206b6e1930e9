using System.Collections.Generic;
using Newtonsoft.Json;
using OnePuz.Definition;
using UnityEngine;

namespace OnePuz.Data
{
    [System.Serializable]
    public class CurrencyData : ISaveData, IVersionedData
    {
        [JsonProperty]
        private Dictionary<int, int> _currencies = new();

        public override void SetupDefaultValues()
        {
            _currencies.Add((int)CurrencyType.COIN, 0);
            _currencies.Add((int)CurrencyType.GEM, 0);
            _currencies.Add((int)CurrencyType.SPIN_TICKET, 0);
        }
        
        public override void ManualSave() { }

        public int GetCurrency(CurrencyType currencyType)
        {
            if (!_currencies.ContainsKey((int)currencyType))
                _currencies.Add((int)currencyType, 0);
            return _currencies[(int)currencyType];
        }

        public void SetCurrency(CurrencyType currencyType, int value)
        {
            var lastQuantity = _currencies[(int)currencyType];
            _currencies[(int)currencyType] = value;
            
            Core.Event.Fire(new CurrencyChangedEvent
            {
                currencyType = currencyType, quantity = _currencies[(int)currencyType], lastQuantity = lastQuantity,
                isInstant = true
            });
        }

        public void AddCurrency(CurrencyType currencyType, int value, bool isInstant = true)
        {
            var lastQuantity = _currencies[(int)currencyType];
            _currencies[(int)currencyType] = Mathf.Max(0, lastQuantity + value);

            Core.Event.Fire(new CurrencyChangedEvent
            {
                currencyType = currencyType, quantity = _currencies[(int)currencyType], lastQuantity = lastQuantity,
                isInstant = isInstant
            });
        }

        public int Version { get; set; } = 0;

        public void Migrate()
        {
            
        }
    }

    public struct CurrencyChangedEvent
    {
        public CurrencyType currencyType;
        public int lastQuantity;
        public int quantity;
        public bool isInstant;
    }
}