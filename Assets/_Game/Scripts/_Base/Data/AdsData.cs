using Newtonsoft.Json;
using OnePuz.Data.Services;
using UnityEngine;

namespace OnePuz.Data
{
    [System.Serializable]
    public class AdsData : ISaveData, IVersionedData
    {
        [RemoteValue("hien_qc")] public bool enableAds;
        [RemoteValue("ads_interval")] public int interstitialInterval; // In seconds
        [RemoteValue("resume_ads")] public bool enableResumeAds;
        [RemoteValue("show_open_ads")] public bool enableAOAForOpenGame;

        [RemoteValue("show_open_ads_first_open")]
        public bool enableAppOpenAdsOnFirstOpen;

        [RemoteValue("show_open_ads_resume")] public bool enableAppOpenAdsForResume;
        public int shownInterstitialCountAllTime;
        [RemoteValue("level_show_inter")] public int showInterstitialFromLevelIndex;

        public bool isAdsRemoved;

        public override void SetupDefaultValues()
        {
            enableAds = true;
            showInterstitialFromLevelIndex = 5 - 1;
            interstitialInterval = 30; // In seconds
            enableResumeAds = true;
            enableAOAForOpenGame = true;
            enableAppOpenAdsOnFirstOpen = false;
            enableAppOpenAdsForResume = false;
            isAdsRemoved = false;
            shownInterstitialCountAllTime = 0;
        }
        
        public override void ManualSave() { }

        public void OnRemoveAds()
        {
            isAdsRemoved = true;
            
            Core.Event.Fire(new OnRemoveAds());
        }

        public int Version { get; set; } = 0;

        public void Migrate()
        {
        }
    }
}