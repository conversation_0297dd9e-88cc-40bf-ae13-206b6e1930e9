using System;
using System.Collections.Generic;
using OnePuz.DailyReward.Data;
using OnePuz.Data.Services;
using OnePuz.Live;
using OP.BlockSand;

namespace OnePuz.Data
{
    public static class DataShortcut
    {
        private static DataService _instance;
        public static DataService Instance => _instance ??= Core.Get<DataService>();

        public static UserData User => Instance.GetData<UserData>("UserData");
        public static LevelData Level => Instance.GetData<LevelData>("LevelData");
        public static BoosterData Booster => Instance.GetData<BoosterData>("BoosterData");
        public static SettingData Setting => Instance.GetData<SettingData>("SettingData");
        public static CurrencyData Currency => Instance.GetData<CurrencyData>("CurrencyData");
        public static DailyRewardData DailyReward => Instance.GetData<DailyRewardData>("DailyRewardData");
        public static RatingData Rating => Instance.GetData<RatingData>("RatingData");
        public static LevelChestData LevelChest => Instance.GetData<LevelChestData>("LevelChestData");
        public static AdsData Ads => Instance.GetData<AdsData>("AdsData");
        public static LiveData Live => Instance.GetData<LiveData>("LiveData");
        public static TutorialData Tutorial => Instance.GetData<TutorialData>("TutorialData");
        public static ScoreData Score => Instance.GetData<ScoreData>("ScoreData");
        public static BoardData Board => Instance.GetData<BoardData>("BoardData");

        public static void ClaimRewards(List<RewardData> rewards, bool isInstant = true)
        {
            for (var i = 0; i < rewards.Count; i++)
            {
                var rewardData = rewards[i];

                switch (rewardData.RewardType)
                {
                    case RewardType.CURRENCY:
                        Currency.AddCurrency(rewardData.CurrencyType, rewardData.Quantity, isInstant);
                        break;
                    case RewardType.BOOSTER:
                        Booster.AddBooster(rewardData.BoosterType, rewardData.Quantity, isInstant);
                        break;
                    case RewardType.SKIN:
                        break;
                    case RewardType.INFINITE_LIVE:
                        Core.Live.EarnInfiniteLive(rewardData.Quantity);
                        break;
                    case RewardType.NO_ADS:
                        if (!Ads.isAdsRemoved)
                        {
                            Ads.OnRemoveAds();
                            Core.Ads.HideBanner();
                        }

                        break;
                    default:
                        throw new ArgumentOutOfRangeException();
                }
            }
        }
    }
}