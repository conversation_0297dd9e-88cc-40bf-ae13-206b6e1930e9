using System.Collections.Generic;
using Newtonsoft.Json;
using OnePuz.Definition;
using UnityEngine;

namespace OnePuz.Data
{
    [System.Serializable]
    public class BoosterData : ISaveData, IVersionedData
    {
        [JsonProperty] private Dictionary<int, int> _boosters = new();

        public override void SetupDefaultValues()
        {
            _boosters.Add((int)BoosterType.BOMB, 0);
            _boosters.Add((int)BoosterType.MORE_SLOTS, 0);
            _boosters.Add((int)BoosterType.SHUFFLE, 0);
            _boosters.Add((int)BoosterType.MAGNET, 0);
        }
        
        public override void ManualSave() { }

        public int GetBooster(BoosterType boosterType)
        {
            if (_boosters.ContainsKey((int)boosterType)) return _boosters[(int)boosterType];
            _boosters.Add((int)boosterType, 0);
            return _boosters[(int)boosterType];
        }

        public void SetBooster(BoosterType boosterType, int value)
        {
            var lastQuantity = _boosters[(int)boosterType];
            _boosters[(int)boosterType] = value;

            Core.Event.Fire(new BoosterChangedEvent
            {
                boosterType = boosterType, quantity = _boosters[(int)boosterType], lastQuantity = lastQuantity,
                isInstant = true
            });
        }

        public void AddBooster(BoosterType boosterType, int value, bool isInstant = true)
        {
            var lastQuantity = _boosters[(int)boosterType];
            _boosters[(int)boosterType] = Mathf.Max(0, lastQuantity + value);

            Core.Event.Fire(new BoosterChangedEvent
            {
                boosterType = boosterType, quantity = _boosters[(int)boosterType], lastQuantity = lastQuantity,
                isInstant = isInstant
            });
        }

        public void UseBooster(BoosterType boosterType)
        {
            AddBooster(boosterType, -1, true);
        }

        public int Version { get; set; } = 0;

        public void Migrate()
        {
        }
    }

    public struct BoosterChangedEvent
    {
        public BoosterType boosterType;
        public int lastQuantity;
        public int quantity;
        public bool isInstant;
    }
}