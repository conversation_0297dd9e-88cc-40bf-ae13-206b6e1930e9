using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using OnePuz.Definition;
using OnePuz.Extensions;

namespace OnePuz.Data
{
    public class TutorialData : ISaveData, IVersionedData
    {
        public enum TutorialStep : byte
        {
            GUIDE_UNDO, GUIDE_MORE_SLOTS, G<PERSON>DE_SHUFFLE, GUIDE_MAGNET
        }

        [JsonProperty]
        private List<TutorialStep> m_PassedSteps;
        
        public override void SetupDefaultValues()
        {
            m_PassedSteps = new List<TutorialStep>();
        }
        
        public override void ManualSave() { }

        int IVersionedData.Version { get; set; } = 0;
        void IVersionedData.Migrate() { }

        public int GetLevelGuiding(TutorialStep step) => step switch
        {
            TutorialStep.GUIDE_UNDO => Core.Definition.Booster.GetDefinition(BoosterType.BOMB).levelUnlock,
            TutorialStep.GUIDE_MORE_SLOTS => Core.Definition.Booster.GetDefinition(BoosterType.MORE_SLOTS).levelUnlock,
            TutorialStep.GUIDE_SHUFFLE => Core.Definition.Booster.GetDefinition(BoosterType.SHUFFLE).levelUnlock,
            TutorialStep.GUIDE_MAGNET => Core.Definition.Booster.GetDefinition(BoosterType.MAGNET).levelUnlock,
            _ => throw new ArgumentOutOfRangeException()
        };

        public bool IsPassed(TutorialStep step) => m_PassedSteps.Contains(step);
        
        public bool IsNecessary(TutorialStep step) => DataShortcut.Level.Current >= GetLevelGuiding(step) && !IsPassed(step);
        
        public bool IsBoosterNecessary(out BoosterType booster)
        {
            if (IsNecessary(TutorialStep.GUIDE_UNDO))
            {
                booster = BoosterType.BOMB;
                return true;
            }
            if (IsNecessary(TutorialStep.GUIDE_MORE_SLOTS))
            {
                booster = BoosterType.MORE_SLOTS;
                return true;
            }
            if (IsNecessary(TutorialStep.GUIDE_SHUFFLE))
            {
                booster = BoosterType.SHUFFLE;
                return true;
            }
            if (IsNecessary(TutorialStep.GUIDE_MAGNET))
            {
                booster = BoosterType.MAGNET;
                return true;
            }

            booster = default;
            return false;
        } 

        public void Save(TutorialStep step) => m_PassedSteps.TryAdd(step);
    }
}