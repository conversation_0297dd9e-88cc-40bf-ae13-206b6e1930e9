namespace OnePuz.Data
{
    [System.Serializable]
    public class UserData : ISaveData, IVersionedData
    {
        public string name = "Player";
        public int userLevel = 0;
        public int openedGameTime;
        public bool alreadyPlayedThisSession;

        public override void SetupDefaultValues()
        {
            name = "Player";
            userLevel = 0;
            openedGameTime = 0;
        }
        
        public override void ManualSave() { }

        public int Version { get; set; }

        public void Migrate()
        {
            if (Version < 1)
            {
            }
        }
    }
}