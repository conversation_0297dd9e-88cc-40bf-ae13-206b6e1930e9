using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using UnityEngine;

namespace OnePuz.UI
{
    public static class UIShortcut
    {
        private static UIService _instance;
        public static UIService Instance => _instance ??= Core.Get<UIService>();

        #region General

        public static void ShowPanel(string panelKey, PanelArgs args = null) => Instance.ShowPanel(panelKey, args);
        public static void ClosePanel(string panelKey) => Instance.ClosePanel(panelKey);
        public static async UniTask ClosePanelAsync(string panelKey) => await Instance.CloseAsync(panelKey);
        public static void ShowPopup(string popupKey, PanelArgs args = null) => Instance.ShowPopup(popupKey, args);

        public static void EnqueuePopup(string id, PanelArgs args = null, bool isPriority = false)
        {
            args ??= PanelArgs.Popup;
            args.mode = ShowMode.APPEND;

            Core.UI.Show(id, args, isPriority);
        }

        public static void EnqueuePopupWithPriority(string id, PanelArgs args = null)
        {
            EnqueuePopup(id, args, true);
        }

        public static void ShowPopupHistory(string popupKey) => Instance.ShowPopup(popupKey, PanelArgs.Popup.WithMode(ShowMode.HISTORY));

        #endregion

        #region Transition

        public static async UniTask ShowTransitionAsync() => await Instance.ShowPanelAsync(UIKeys.Panel.TRANSITION);
        public static async UniTask CloseTransitionAsync() => await Instance.CloseAsync(UIKeys.Panel.TRANSITION);

        #endregion

        #region Obtaining Rewards

        private static UIObtainRewardPanel _obtainRewardPanel;

        private static UIObtainRewardPanel ObtainRewardPanel
        {
            get => _obtainRewardPanel ?? Instance.Get<UIObtainRewardPanel>();
            set => _obtainRewardPanel = value;
        }

        public static void ShowCongratulation<T>(List<RewardData> rewards) where T : class, IPanel, IContainRewardTarget
        {
            ShowCongratulationAsync(rewards, new IContainRewardTarget[] { Instance.Get<T>() }).Forget();
        }

        public static async UniTask ShowCongratulationAsync(List<RewardData> rewards, IContainRewardTarget[] targetPanels = null)
        {
            var args = PanelArgs.Popup.AddData("reward_data", rewards);

            if (targetPanels != null && targetPanels.Length > 0)
            {
                args.data.Add("target_panels", targetPanels);
            }

            await Instance.ShowPopupAsync(UIKeys.Panel.PANEL_CONGRATULATION, args);
        }

        public static void ShowCongratulation<T>(UITreasure treasure) where T : class, IPanel, IContainRewardTarget
        {
            ShowCongratulationAsync(treasure, new IContainRewardTarget[] { Instance.Get<T>() }).Forget();
        }

        public static async UniTask ShowCongratulationAsync(UITreasure treasure, IContainRewardTarget[] targetPanels = null)
        {
            var args = PanelArgs.Popup.AddData("treasure", treasure);

            if (targetPanels != null && targetPanels.Length > 0)
            {
                args.data.Add("target_panels", targetPanels);
            }

            await Instance.ShowPopupAsync(UIKeys.Panel.PANEL_CONGRATULATION, args);
        }

        public static UIRewardItem SpawnRewardItem(RewardData rewardData, Transform container)
        {
            return ObtainRewardPanel.SpawnRewardItem(rewardData, container);
        }

        public static async UniTask ClaimRewardsAsync(List<UIRewardItem> rewardItems, IContainRewardTarget[] targetPanels)
        {
            await ObtainRewardPanel.ClaimAsync(rewardItems, targetPanels);
        }
        
        public static void ClaimRewards(List<UIRewardItem> rewardItems, params IContainRewardTarget[] targetPanels)
        {
            ObtainRewardPanel.ClaimAsync(rewardItems, targetPanels).Forget();
        }

        public static async UniTask ClaimRewardsAsync<T>(List<RewardData> rewards, Vector2 position) where T : class, IPanel, IContainRewardTarget
        {
            await ObtainRewardPanel.ClaimAsync(rewards, position, Instance.Get<T>());
        }
        
        public static void ClaimRewards<T>(List<RewardData> rewards, Vector2 position) where T : class, IPanel, IContainRewardTarget
        {
            ObtainRewardPanel.ClaimAsync(rewards, position, Instance.Get<T>()).Forget();
        }

        #endregion

        public static void ShowMessage(string title, string message, ShowMode mode = ShowMode.HISTORY) => Instance.ShowPopup(
            UIKeys.Panel.POPUP_MESSAGE,
            PanelArgs.Popup
                .WithMode(mode)
                .AddData("message", message)
                .AddData("title", title));

        public static void ShowNoAdsAvailable()
            => ShowMessage(
                "No Ads Available",
                "We were unable to find any ads to show! Please try again shortly.");
    }
}