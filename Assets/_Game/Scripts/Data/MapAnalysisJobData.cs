using Unity.Collections;

namespace OP.BlockSand
{
    /// <summary>
    /// Data container for MapAnalysisJob operations
    /// Contains material distribution and surface coverage analysis results
    /// </summary>
    public struct MapAnalysisJobData
    {
        // Output data from MapAnalysisJob
        public NativeArray<int> materialPixelCounts;        // Count of pixels for each material index
        public NativeArray<int> materialSurfacePixelCounts; // Count of surface pixels for each material index  
        public NativeArray<float> materialPercentages;      // Percentage of each material index
        public NativeArray<int> totalNonEmptyPixels;        // [0] = total non-empty pixels
        
        public bool IsValid => materialPixelCounts.IsCreated;
        
        /// <summary>
        /// Initialize the job data with specified maximum material index
        /// Uses Persistent allocator to avoid TempJob lifetime issues
        /// </summary>
        public static MapAnalysisJobData Create(int maxMaterialIndex)
        {
            return new MapAnalysisJobData
            {
                materialPixelCounts = new NativeArray<int>(maxMaterialIndex + 1, Allocator.Persistent),
                materialSurfacePixelCounts = new NativeArray<int>(maxMaterialIndex + 1, Allocator.Persistent),
                materialPercentages = new NativeArray<float>(maxMaterialIndex + 1, Allocator.Persistent),
                totalNonEmptyPixels = new NativeArray<int>(1, Allocator.Persistent)
            };
        }
        
        /// <summary>
        /// Dispose all native arrays
        /// </summary>
        public void Dispose()
        {
            if (materialPixelCounts.IsCreated) materialPixelCounts.Dispose();
            if (materialSurfacePixelCounts.IsCreated) materialSurfacePixelCounts.Dispose();
            if (materialPercentages.IsCreated) materialPercentages.Dispose();
            if (totalNonEmptyPixels.IsCreated) totalNonEmptyPixels.Dispose();
        }
        
        /// <summary>
        /// Get pixel count for a specific material index
        /// </summary>
        public int GetPixelCount(int materialIndex)
        {
            if (!IsValid || materialIndex < 0 || materialIndex >= materialPixelCounts.Length)
                return 0;
            return materialPixelCounts[materialIndex];
        }
        
        /// <summary>
        /// Get surface pixel count for a specific material index
        /// </summary>
        public int GetSurfacePixelCount(int materialIndex)
        {
            if (!IsValid || materialIndex < 0 || materialIndex >= materialSurfacePixelCounts.Length)
                return 0;
            
            // for (var i = 0; i < materialSurfacePixelCounts.Length; i++)
            // {
            //     OLogger.Log($"materialSurfacePixelCounts[{i}] = {materialSurfacePixelCounts[i]}");
            // }
            return materialSurfacePixelCounts[materialIndex];
        }
        
        /// <summary>
        /// Get percentage for a specific material index
        /// </summary>
        public float GetPercentage(int materialIndex)
        {
            if (!IsValid || materialIndex < 0 || materialIndex >= materialPercentages.Length)
                return 0f;
            return materialPercentages[materialIndex];
        }
        
        /// <summary>
        /// Get total non-empty pixels count
        /// </summary>
        public int GetTotalNonEmptyPixels()
        {
            if (!IsValid) return 0;
            return totalNonEmptyPixels[0];
        }
        
        /// <summary>
        /// Get material index with highest surface pixel count
        /// </summary>
        public int GetMaterialIndexWithHighestSurfaceCount()
        {
            if (!IsValid) return -1;
            
            var maxCount = 0;
            var maxIndex = -1;
            
            for (int i = 0; i < materialSurfacePixelCounts.Length; i++)
            {
                if (materialSurfacePixelCounts[i] > maxCount)
                {
                    maxCount = materialSurfacePixelCounts[i];
                    maxIndex = i;
                }
            }
            
            return maxIndex;
        }
        
        /// <summary>
        /// Get material indices sorted by surface pixel count (descending)
        /// </summary>
        public NativeArray<int> GetMaterialIndicesSortedBySurfaceCount(Allocator allocator)
        {
            if (!IsValid) return new NativeArray<int>(0, allocator);

            var indices = new NativeArray<int>(materialSurfacePixelCounts.Length, allocator);
            var counts = new NativeArray<int>(materialSurfacePixelCounts.Length, Allocator.Temp);

            try
            {
                // Initialize indices and copy counts
                for (var i = 0; i < indices.Length; i++)
                {
                    indices[i] = i;
                    counts[i] = materialSurfacePixelCounts[i];
                }

                // Simple bubble sort (good enough for small arrays)
                for (var i = 0; i < indices.Length - 1; i++)
                {
                    for (var j = 0; j < indices.Length - i - 1; j++)
                    {
                        if (counts[j] >= counts[j + 1]) continue;
                        
                        // Swap counts
                        (counts[j], counts[j + 1]) = (counts[j + 1], counts[j]);

                        // Swap indices
                        (indices[j], indices[j + 1]) = (indices[j + 1], indices[j]);
                    }
                }

                return indices;
            }
            finally
            {
                // Always dispose temp array
                if (counts.IsCreated)
                    counts.Dispose();
            }
        }
    }
}
