using UnityEngine;

namespace OP.BlockSand
{
    /// <summary>
    /// Animation data for clearing pixels
    /// </summary>
    public struct ClearAnimationData
    {
        public Vector2Int position;
        public Color32 originalColor;
        public PixelMaterialId materialId;
        public float animationTime;
        public bool isAnimating;
        
        public ClearAnimationData(Vector2Int pos, Color32 color, PixelMaterialId material)
        {
            position = pos;
            originalColor = color;
            materialId = material;
            animationTime = 0f;
            isAnimating = true;
        }
    }
}