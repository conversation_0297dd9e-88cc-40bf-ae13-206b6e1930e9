using Unity.Collections;

namespace OP.BlockSand
{
    /// <summary>
    /// Status of game state checking process
    /// </summary>
    public enum GameStateCheckStatus
    {
        Idle,
        CheckingLose,
        CheckingClusters,
        ClearingClusters,
        AnalyzingMap,
        Completed
    }
    
    /// <summary>
    /// Data container for async job operations
    ///
    /// Note: clusterCount and clearableCount must be NativeArray because Unity Job System
    /// requires all output data from job has to be NativeArray so can write thread-safe.
    /// Can not use ref int or out int with jobs.
    /// </summary>
    public struct GameStateJobData
    {
        // Lose check data
        public NativeArray<bool> hasLost;           // [0] = has lost
        public NativeArray<bool> inDanger;           // [0] = in danger
        public NativeArray<int> firstLosePixelX;   // [0] = first lose pixel X
        public NativeArray<int> firstLosePixelY;   // [0] = first lose pixel Y

        // Flood fill data
        public NativeArray<int> clusterMap;        // Maps pixel index to cluster ID
        public NativeArray<ClusterData> clusters;  // Array of cluster data
        public NativeArray<int> clearableClusters; // List of cleanable cluster IDs
        public NativeArray<int> clusterCount;      // [0] = total clusters found
        public NativeArray<int> clearableCount;    // [0] = cleanable clusters count
        public NativeArray<int> activeMaterialIndices; // List of active material Indices to check

        // Pixel buffer copy for jobs (to avoid race conditions)
        public NativeArray<Pixel> pixelBufferCopy;

        // Map analysis data
        public MapAnalysisJobData mapAnalysisData;

        public bool IsValid => hasLost.IsCreated;

        public void Dispose()
        {
            if (inDanger.IsCreated) inDanger.Dispose();
            if (hasLost.IsCreated) hasLost.Dispose();
            if (firstLosePixelX.IsCreated) firstLosePixelX.Dispose();
            if (firstLosePixelY.IsCreated) firstLosePixelY.Dispose();
            if (clusterMap.IsCreated) clusterMap.Dispose();
            if (clusters.IsCreated) clusters.Dispose();
            if (clearableClusters.IsCreated) clearableClusters.Dispose();
            if (clusterCount.IsCreated) clusterCount.Dispose();
            if (clearableCount.IsCreated) clearableCount.Dispose();
            if (activeMaterialIndices.IsCreated) activeMaterialIndices.Dispose();
            if (pixelBufferCopy.IsCreated) pixelBufferCopy.Dispose();
            if (mapAnalysisData.IsValid) mapAnalysisData.Dispose();
        }
    }
}