using UnityEngine;

namespace OP.BlockSand
{
    /// <summary>
    /// Data structure to hold information about a sand cluster
    /// </summary>
    public struct ClusterData
    {
        public int clusterId;
        public int materialIndex; // Store material index instead of material ID
        public bool spansFromLeftToRight;
        public int pixelCount; // Total pixels in cluster (including unstable ones)
        public int validPixelCount; // Only stable/valid pixels in cluster
        public int minX;
        public int maxX;
        public int minY;
        public int maxY;

        public ClusterData(int id, int materialIdx)
        {
            clusterId = id;
            materialIndex = materialIdx;
            spansFromLeftToRight = false;
            pixelCount = 0;
            validPixelCount = 0;
            minX = int.MaxValue;
            maxX = int.MinValue;
            minY = int.MaxValue;
            maxY = int.MinValue;
        }

        public void AddPixel(int x, int y, bool isValid)
        {
            pixelCount++;
            if (isValid)
                validPixelCount++;

            minX = Mathf.Min(minX, x);
            maxX = Mathf.Max(maxX, x);
            minY = Mathf.Min(minY, y);
            maxY = Mathf.Max(maxY, y);
        }

        public void CheckSpansFromLeftToRight(int worldMinX, int worldMaxX)
        {
            // Check if cluster spans from left boundary to right boundary
            // A cluster spans if it touches or is very close to both boundaries
            // worldMinX = 1 (after left boundary), worldMaxX = worldWidth - 2 (before right boundary)
            spansFromLeftToRight = minX <= worldMinX && maxX >= worldMaxX;
        }

        /// <summary>
        /// Check if cluster has enough valid pixels to be clearable
        /// Requires at least 70% of pixels to be valid/stable
        /// </summary>
        public bool HasSufficientValidPixels()
        {
            if (pixelCount == 0) return false;
            return validPixelCount == pixelCount;
            // var validRatio = (float)validPixelCount / pixelCount;
            // return validRatio >= 0.7f; // At least 70% of pixels must be stable
        }
    }
}
