using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;

namespace OP.BlockSand
{
    public struct OnMapAnalysisJobFinishedEvent
    {
        public MapAnalysisJobData analysisResults;
    }
    
    /// <summary>
    /// Job to analyze the map and calculate material distribution and surface coverage
    /// </summary>
    [BurstCompile]
    public struct MapAnalysisJob : IJob
    {
        // Input data
        [ReadOnly] public int worldWidth;
        [ReadOnly] public int worldHeight;
        [ReadOnly] public NativeArray<Pixel> pixelBuffer;
        [ReadOnly] public int maxMaterialIndex; // Maximum material index to analyze
        
        // Output data
        public NativeArray<int> materialPixelCounts; // Count of pixels for each material index
        public NativeArray<int> materialSurfacePixelCounts; // Count of surface pixels for each material index
        public NativeArray<float> materialPercentages; // Percentage of each material index
        public NativeArray<int> totalNonEmptyPixels; // [0] = total non-empty pixels
        
        private const int MARGIN = 1;
        
        public void Execute()
        {
            // Initialize arrays
            for (int i = 0; i < materialPixelCounts.Length; i++)
            {
                materialPixelCounts[i] = 0;
                materialSurfacePixelCounts[i] = 0;
                materialPercentages[i] = 0f;
            }
            totalNonEmptyPixels[0] = 0;
            
            // First pass: Count all pixels for each material index
            for (var y = MARGIN; y < worldHeight - MARGIN; y++)
            {
                for (var x = MARGIN; x < worldWidth - MARGIN; x++)
                {
                    var index = GetIndex(x, y);
                    var pixel = pixelBuffer[index];
                    
                    if (pixel.IsEmpty()) continue;
                    
                    totalNonEmptyPixels[0]++;
                    
                    // Count pixel for this material index
                    var materialIndex = pixel.materialIndex;
                    if (materialIndex >= 0 && materialIndex < materialPixelCounts.Length)
                    {
                        materialPixelCounts[materialIndex]++;
                    }
                }
            }
            
            // Second pass: Count surface pixels (pixels that have empty space above them)
            for (var y = MARGIN; y < worldHeight - MARGIN - 1; y++) // -1 to avoid checking above world bounds
            {
                for (var x = MARGIN; x < worldWidth - MARGIN; x++)
                {
                    var index = GetIndex(x, y);
                    var pixel = pixelBuffer[index];
                    
                    if (pixel.IsEmpty()) continue;
                    
                    // Check if pixel above is empty (surface pixel)
                    var aboveIndex = GetIndex(x, y + 1);
                    var pixelAbove = pixelBuffer[aboveIndex];
                    
                    if (pixelAbove.IsEmpty())
                    {
                        var materialIndex = pixel.materialIndex;
                        if (materialIndex >= 0 && materialIndex < materialSurfacePixelCounts.Length)
                        {
                            materialSurfacePixelCounts[materialIndex]++;
                        }
                    }
                }
            }
            
            // Calculate percentages
            var totalPixels = totalNonEmptyPixels[0];
            if (totalPixels > 0)
            {
                for (int i = 0; i < materialPercentages.Length; i++)
                {
                    materialPercentages[i] = (float)materialPixelCounts[i] / totalPixels * 100f;
                }
            }
        }
        
        private int GetIndex(int x, int y)
        {
            return y * worldWidth + x;
        }
    }
}
