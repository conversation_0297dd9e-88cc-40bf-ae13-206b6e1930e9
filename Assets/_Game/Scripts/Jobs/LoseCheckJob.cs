using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;

namespace OP.BlockSand
{
    /// <summary>
    /// Job to check if the game should end (lose condition)
    /// Checks if any sleeping pixels are at row 90 or above
    /// </summary>
    [BurstCompile]
    public struct LoseCheckJob : IJob
    {
        // Input data
        [ReadOnly] public int worldWidth;
        [ReadOnly] public int worldHeight;
        [ReadOnly] public int loseThresholdY;
        [ReadOnly] public int inDangerThresholdY;
        [ReadOnly] public NativeArray<Pixel> pixelBuffer;

        // Output data
        public NativeArray<bool> inDanger;
        public NativeArray<bool> hasLost;
        public NativeArray<int> firstLosePixelX;
        public NativeArray<int> firstLosePixelY;

        private const int MARGIN = 1;

        public void Execute()
        {
            // Start checking from the top of the world downwards
            // This is more efficient as we can early exit when we find the first non-empty row
            
            bool localInDanger = false;
            
            for (var y = worldHeight - 1 - MARGIN; y >= MARGIN; y--)
            {
                var hasPixelsInThisRow = false;

                // Check if this row has any non-empty pixels
                for (var x = MARGIN; x < worldWidth - MARGIN; x++)
                {
                    var index = GetIndex(x, y);
                    var pixel = pixelBuffer[index];

                    if (pixel.IsEmpty()) continue;
                    hasPixelsInThisRow = true;

                    if (y >= inDangerThresholdY)
                    {
                        localInDanger = true;
                    }

                    // If this pixel is sleeping, and we're at or above the loss or danger threshold
                    if (pixel.IsFreeFalling() || pixel.sleepCounter < 5 || y < loseThresholdY) continue;
                    
                    hasLost[0] = true;
                    firstLosePixelX[0] = x;
                    firstLosePixelY[0] = y;
                    return;
                }

                // If we found pixels in this row, but we're below the loss threshold,
                // we can stop checking as all pixels below will also be below the threshold
                if (hasPixelsInThisRow && y < loseThresholdY)
                {
                    break;
                }
            }

            // No losing condition found
            hasLost[0] = false;
            inDanger[0] = localInDanger;
            firstLosePixelX[0] = -1;
            firstLosePixelY[0] = -1;
        }

        private int GetIndex(int x, int y)
        {
            return y * worldWidth + x;
        }
    }
}