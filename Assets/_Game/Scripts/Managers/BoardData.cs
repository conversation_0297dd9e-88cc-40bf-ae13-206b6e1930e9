using System.Collections.Generic;
using Newtonsoft.Json;
using OnePuz.Data;

namespace OP.BlockSand
{
    public class BoardData : ISaveData, IVersionedData
    {
        [System.Serializable]
        public class Pixel
        {
            public int x;
            public int y;
            
            public byte r;
            public byte g;
            public byte b;
            public byte a;
            
            public int materialIndex;
        }
        
        [System.Serializable]
        public class Shape
        {
            public string id;
            public List<BlockType> blockTypes;
        }
        
        [JsonProperty]
        public List<Pixel> activePixels = new List<Pixel>();
        [JsonProperty]
        public List<Shape> savedShapes = new List<Shape>();
        
        
        public override void SetupDefaultValues()
        {
            activePixels = new List<Pixel>();
            savedShapes = new List<Shape>();
        }
        
        public override void ManualSave()
        {
            if (Core.Cache == null || Core.GameManager == null || Core.GameManager.PixelWorld == null || !Core.GameManager.PixelWorld.LoadingFinished)
                return;
            
            activePixels.Clear();
            foreach (var chunk in Core.GameManager.PixelWorld.ActiveChunks)
            {
                if (!chunk.LoadSucceeded || !chunk.Pixels.IsCreated) continue;
                for (var i = 0; i < chunk.Pixels.Length; i++)
                {
                    var pixel = chunk.Pixels[i];
                    if (pixel.IsEmpty()) continue;
                    activePixels.Add(new Pixel()
                    {
                        x = pixel.x,
                        y = pixel.y,
                        r = pixel.r,
                        g = pixel.g,
                        b = pixel.b,
                        a = pixel.a,
                        materialIndex = pixel.materialIndex
                    });
                }
            }
            
            savedShapes = Core.GameManager.ShapeSpawner.GetSavedData();
        }

        public int Version { get; set; }
        public void Migrate()
        {
            
        }
    }
}