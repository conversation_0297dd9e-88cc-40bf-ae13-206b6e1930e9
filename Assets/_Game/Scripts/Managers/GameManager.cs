using Cysharp.Threading.Tasks;
using DrawXXL;
using OnePuz.Audio;
using OnePuz.Extensions;
using OnePuz.Manager;
using OnePuz.UI;
using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.EventSystems;

namespace OP.BlockSand
{
    public struct OnGameViewChangedEvent
    {
    }
    
    public struct OnPlacedShapeEvent
    {
        public int pixelCount;
        public Vector3 worldPosition;
    }
    
    public struct OnClearSandClusterEvent
    {
        public int pixelCount;
        public PixelMaterialId materialId;
        public Vector3 worldPosition;
    }
    
    public class GameManager : BaseManager
    {
        [SerializeField]
        private Camera _camera;

        [SerializeField]
        private SandWorld _sandWorld;
        public PixelWorld PixelWorld => _sandWorld.PixelWorld;

        [SerializeField]
        private PixelMaterialId _drawingMaterialId = PixelMaterialId.Sand;

        [SerializeField]
        private ShapeSpawner _shapeSpawner;
        public ShapeSpawner ShapeSpawner => _shapeSpawner;

        [SerializeField]
        private PlayerInput _playerInput;

        [SerializeField]
        private Board _board;
        public Board Board => _board;
        
        [SerializeField]
        private Vector2 _gameViewSizeInUnit;
        
        [Header("Combo")]
        [SerializeField]
        private ComboController _comboController;

        private Vector3? _lastMousePixelPos;

        private bool _canPlaceBlock;
        private bool _placingBlock;

        public override async UniTask LoadAsync()
        {
            _sandWorld.PixelWorld.FrameRate = 60;
            await UniTask.Yield();
            var loading = true;
            _sandWorld.LoadLevel(0, _ => loading = false);
            while (loading)
                await UniTask.Yield();

            _shapeSpawner.Init(new Vector2Int(_sandWorld.Width, _sandWorld.Height));
            _playerInput.Init(_camera);
            await _board.InitAsync(_sandWorld);

            this.EventSubscribe<OnShapeSelectedEvent>(HandleSelectShape);
            this.EventSubscribe<OnShapeMovedEvent>(HandleMoveShape);
            this.EventSubscribe<OnShapeReleasedEvent>(HandleReleaseShape);
            this.EventSubscribe<OnShownPanelEvent>(HandleShownPanel);
            this.EventSubscribe<OnGotComboEvent>(HandleOnGotCombo);
        }

        public override UniTask UnloadAsync()
        {
            this.EventUnsubscribe<OnShapeSelectedEvent>(HandleSelectShape);
            this.EventUnsubscribe<OnShapeMovedEvent>(HandleMoveShape);
            this.EventUnsubscribe<OnShapeReleasedEvent>(HandleReleaseShape);
            this.EventUnsubscribe<OnShownPanelEvent>(HandleShownPanel);
            this.EventUnsubscribe<OnGotComboEvent>(HandleOnGotCombo);
            return UniTask.CompletedTask;
        }

        public override async UniTask ResetAsync()
        {
            _shapeSpawner.Reset();
            _shapeSpawner.SpawnShapesAsync(true).Forget();

            _board.ResetGameState();
            Core.Score.Reset();

            var loading = true;
            _sandWorld.LoadLevel(0, _ => loading = false);
            while (loading)
                await UniTask.Yield();

            _canPlaceBlock = true;
            _placingBlock = false;
        }

        public override void Activate()
        {
            _canPlaceBlock = true;

            _shapeSpawner.ShowAllShapes();
        }

        private void Update()
        {
            if (!IsInitialized)
                return;

            // HandleInput();
            _board.UpdateGameStateCheck();
            if (_board.ShouldCheckGameState())
            {
                _board.CheckGameState();
            }
        }

        private void HandleInput()
        {
            // For testing purposes
            if (Input.touchCount > 0)
            {
                var touch = Input.GetTouch(0);
                var pixelPos = _sandWorld.PixelWorld.ScreenToPixelPos(touch.position);
                DrawAt(bigBrush: false, pixelPos);
            }
            else if (
                ((Input.GetMouseButton(0) && !Input.GetKey(KeyCode.LeftControl))
                 || (Input.GetMouseButtonDown(0) && Input.GetKey(KeyCode.LeftControl)))
                && !EventSystem.current.IsPointerOverGameObject()
            )
            {
                var isShiftPressed = Input.GetKey(KeyCode.LeftShift);
                var pixelPos = _sandWorld.PixelWorld.MouseToPixelPos(Input.mousePosition);
                DrawAt(isShiftPressed, pixelPos);
            }
            else
            {
                _lastMousePixelPos = null;
            }
        }

        private void HandleSelectShape(OnShapeSelectedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;
            AudioShortcut.PlayChooseBlock();
            Core.Vibration.VibrateLight();
            shape.HandleSelected(e.worldPosition, _board.DrawableRect);
        }

        private void HandleMoveShape(OnShapeMovedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;

            shape.HandleMoving(e.worldPosition, _board.DrawableRect);
            _canPlaceBlock = _board.ValidateDrawShape(shape, out _, out _);

            if (_placingBlock)
                return;
        }

        private void HandleReleaseShape(OnShapeReleasedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;

            shape.HandleRelease();

            if (_canPlaceBlock && !_placingBlock && !e.hasCanceled)
            {
                PlaceShapeAsync(shape, e.shapeIndex).Forget();
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();
                shape.MoveBack();
            }
        }

        private async UniTask PlaceShapeAsync(Shape shape, int shapeIndex)
        {
            _placingBlock = true;
            _canPlaceBlock = false;

            var valid = _board.ValidateDrawShape(shape, out var targetShapePosition, out var pixelPosition);
            if (valid)
            {
                var shapeMoving = true;
                shape.MoveToTarget(targetShapePosition, () => shapeMoving = false);
                await UniTask.WaitUntil(() => !shapeMoving);

                Core.Vibration.VibrateLight();
                _board.DrawShape(shape, pixelPosition);
                _shapeSpawner.RemoveShape(shapeIndex);
                _shapeSpawner.Despawn(shape);
                AudioShortcut.PlayDropSand();
                
                Core.Event.Fire(new OnPlacedShapeEvent() { pixelCount = shape.GetPixelCount(), worldPosition = targetShapePosition });

                if (!_shapeSpawner.AnyShapesLeft())
                    _shapeSpawner.SpawnShapesAsync(showImmediately: true).Forget();
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();
                shape.MoveBack();
            }

            _placingBlock = false;
            _canPlaceBlock = true;
        }

        private void HandleShownPanel(OnShownPanelEvent e)
        {
            if (e.panelId != UIKeys.Panel.GAME) return;
            var gamePanel = Core.UI.Get<UIPanelGame>();
            if (!gamePanel) return;
            var gameViewRectTransform = gamePanel.GetGameBounds();
            var gameBound = gameViewRectTransform.rect;
            var canvasBound = gamePanel.pCanvas.GetComponent<RectTransform>().rect;
            var gameViewRatio = gameBound.height / canvasBound.height;
            var orthoSize = SetupMultiResolution(gameViewRatio);

            var boardPositionY = gameViewRectTransform.anchoredPosition.y / canvasBound.height * 2f * orthoSize + 1.0f;
            _sandWorld.transform.position = new Vector3(0, boardPositionY, 0);
            
            Core.Event.Fire(new OnGameViewChangedEvent());
        }

        private float SetupMultiResolution(float ratio = 0.68f)
        { 
            var expectedOrthoSize = _gameViewSizeInUnit.y / ratio / 2f;
            var width = expectedOrthoSize * 2f * _camera.aspect;
            if (width < _gameViewSizeInUnit.x)
            {
                expectedOrthoSize = _gameViewSizeInUnit.x / 2f / _camera.aspect;
            }
            _camera.orthographicSize = expectedOrthoSize;
            return expectedOrthoSize;
        }
        
        private void HandleOnGotCombo(OnGotComboEvent e)
        {
            if (e.combo <= 1) return;
            _comboController.Show(e.combo);
        }

        [Button]
        private void TestDraw(Vector2 pixelPos)
        {
            DrawAt(false, pixelPos);
        }

        private void DrawAt(bool bigBrush, Vector3 pixelPos)
        {
            // if (!_lastMousePixelPos.HasValue)
            // {
            //     _sandWorld.PixelWorld.DrawBrushAt(PixelWorldBrushShape.Circle, bigBrush ? 20 : 5, pixelPos.x, pixelPos.y, _drawingMaterialId);
            // }
            // else
            // {
            //     _sandWorld.PixelWorld.DrawLine(PixelWorldBrushShape.Circle, bigBrush ? 20 : 5, _lastMousePixelPos.Value.x, _lastMousePixelPos.Value.y, pixelPos.x, pixelPos.y, _drawingMaterialId);
            // }

            _sandWorld.PixelWorld.DrawPixelAt(pixelPos.x, pixelPos.y, _drawingMaterialId);

            if (_sandWorld.PixelWorld.TryGetPixelAt(pixelPos, out Pixel pixel))
            {
                OLogger.LogNotice($"==> Check pixel at {pixelPos}: Material: {pixel.materialIndex}, Pos: {pixel.x} - {pixel.y}");
            }

            _lastMousePixelPos = pixelPos;
        }
    }
}