using System;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockSand
{
    public struct OnRequestSpawningShapesEvent
    {
    }

    public class ShapeSpawner : MonoBehaviour
    {
        [SerializeField]
        private ShapeDefinition _shapeDefinition;

        [SerializeField]
        private PixelMaterials _materialDefinition;

        [SerializeField]
        private Transform _shapeContainer;

        [SerializeField]
        private Transform[] _spawnPoints;

        private readonly Shape[] _shapes = new Shape[3];

        private ShapeSpawnRule _spawnRule;
        private Vector2Int _boardSize;
        private bool _hasFinishedAnalysis = false;
        
        public bool UnlockedNewColor { get; set; }
        public BlockType NewColor { get; set; }

        public void Init(Vector2Int boardSize)
        {
            _boardSize = boardSize;

            _spawnRule = new ShapeSpawnRule(_materialDefinition, _boardSize.x);
            var currentLevel = DataShortcut.Score.level;
            _spawnRule.SetLevel(currentLevel);

            LoadSavedShapes(DataShortcut.Board.savedShapes);

            // Subscribe to score level up events to update spawn rules
            this.EventSubscribe<OnScoreLevelUpEvent>(HandleScoreLevelUp);
            this.EventSubscribe<OnMapAnalysisJobFinishedEvent>(HandleMapAnalysisFinished);
            _hasFinishedAnalysis = false;
        }

        public void LoadSavedShapes(List<BoardData.Shape> savedShapes)
        {
            if (savedShapes == null || savedShapes.Count == 0)
            {
                SpawnShapesAsync().Forget();
                return;
            }

            for (var i = 0; i < savedShapes.Count; i++)
            {
                if (savedShapes[i] == null) continue;
                _shapes[i] = SpawnShape(i, _shapeDefinition.shapes.First(t => t.id == savedShapes[i].id), savedShapes[i].blockTypes);
            }
        }

        [Button]
        public async UniTask SpawnShapesAsync(bool showImmediately = false)
        {
            // OLogger.LogNotice($"[Start] === Shape Spawn Debug === ");
            _hasFinishedAnalysis = false;
            var startTime = Time.time;
            Core.Event.Fire(new OnRequestSpawningShapesEvent());
            await UniTask.WaitUntil(() => _hasFinishedAnalysis || Time.time - startTime > 0.5f);

            _spawnRule.OnSpawnAllShapes();

            // OLogger.LogNotice($"[Finish] === Shape Spawn Debug === " +
            //                   $"Score Level: {DataShortcut.Score.level} " +
            //                   $"Current Score: {DataShortcut.Score.currentScore} " +
            //                   $"Spawn Rule: {_spawnRule.GetDebugInfo()}\n" +
            //                   $"Time: {Time.time:F1}s");
            for (var i = 0; i < _shapes.Length; i++)
            {
                var shape = _shapes[i];
                if (!shape) continue;

                Despawn(shape);
                _shapes[i] = null;
            }

            // Use batch rule-based spawning for better special shape positioning
            var batchResults = _spawnRule.GenerateBatchShapeBlockTypes(_shapeDefinition.defaultBlockCount, _spawnPoints.Length);

            for (var i = 0; i < _spawnPoints.Length && i < batchResults.Count; i++)
            {
                var spawnResult = batchResults[i];
                var shapeDatum = _shapeDefinition.GetRandomShape(spawnResult.isMultiColor);

                _shapes[i] = SpawnShape(i, shapeDatum, spawnResult.blockTypes);
                if (showImmediately)
                {
                    _shapes[i].Show();
                }

                // Log spawn info for debugging
                // OLogger.Log($"Spawned shape {i}: {spawnResult.debugInfo}");
            }
        }

        private Shape SpawnShape(int index, ShapeDefinition.Datum datum, List<BlockType> blockTypes)
        {
            var shape = Core.ScenePool.Spawn(_shapeDefinition.shapePrefab.gameObject, _shapeContainer).GetComponent<Shape>();
            shape.OTransform.localPosition = _spawnPoints[index].localPosition;
            shape.Init(datum, blockTypes);
            return shape;
        }

        public void ShowAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                shape.Show();
            }
        }

        public Shape GetShape(int index)
        {
            return _shapes[index];
        }

        public bool AnyShapesLeft()
        {
            return _shapes.Any(t => t);
        }

        public void RemoveShape(int index)
        {
            if (_shapes[index])
            {
                _shapes[index] = null;
            }
        }

        public void Despawn(Shape shape)
        {
            shape.Reset();
            Core.ScenePool.Recycle(shape.gameObject);
        }

        public List<BoardData.Shape> GetSavedData()
        {
            return _shapes.Select(t => t == null ? null : new BoardData.Shape() { id = t.Id, blockTypes = t.BlockTypes }).ToList();
        }

        public void Reset()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                Despawn(shape);
            }

            // Reset the spawn rule system
            _spawnRule.Reset();
        }

        private void HandleScoreLevelUp(OnScoreLevelUpEvent e)
        {
            _spawnRule.SetLevel(e.level);
            
            UnlockedNewColor = false;
            
            var lastLevelConfig = _spawnRule.GetLevelConfig(e.level - 1);
            var currentLevelConfig = _spawnRule.GetLevelConfig(e.level);
            if (lastLevelConfig == null || currentLevelConfig == null) return;
            if (currentLevelConfig.colorCount <= lastLevelConfig.colorCount) return;
            UnlockedNewColor = true;
            NewColor = currentLevelConfig.availableBlockTypes.Last();
        }

        private void HandleMapAnalysisFinished(OnMapAnalysisJobFinishedEvent obj)
        {
            if (!obj.analysisResults.IsValid)
            {
                _hasFinishedAnalysis = true;
                return;
            }
            
            _spawnRule.UpdateMapAnalysis(obj.analysisResults, _boardSize.x);
            _hasFinishedAnalysis = true;
        }

        private void OnDestroy()
        {
            this.EventUnsubscribe<OnScoreLevelUpEvent>(HandleScoreLevelUp);
            this.EventUnsubscribe<OnMapAnalysisJobFinishedEvent>(HandleMapAnalysisFinished);
        }

#if UNITY_EDITOR
        [Button("Show Spawn Debug Info")]
        private void ShowDebugInfo()
        {
            var debugInfo = _spawnRule.GetDebugInfo();
            Debug.Log($"Shape Spawn Debug: {debugInfo}");
        }
#endif
    }
}