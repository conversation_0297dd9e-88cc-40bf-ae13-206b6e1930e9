using OnePuz.Extensions;
using UnityEngine;

namespace OP.BlockSand
{
    public class BackgroundFxAnimation : MonoBehaviour
    {
        private Animator _animator;
        
        private void OnEnable()
        {
            _animator = GetComponent<Animator>();
            this.EventSubscribe<OnClearSandClusterEvent>(HandleClearSandCluster);    
        }

        public void Play(BlockType blockType)
        {
            var index = (int)blockType - 100 + 1;
            _animator.SetTrigger($"color_{index}");
        }
        
        private void HandleClearSandCluster(OnClearSandClusterEvent e)
        {
            Play((BlockType)e.materialId);
        }
        
        private void OnDisable()
        {
            this.EventUnsubscribe<OnClearSandClusterEvent>(HandleClearSandCluster);
        }
    }
}