using System.Collections.Generic;
using OnePuz.Audio;
using UnityEngine;
using PrimeTween;
using TMPro;

namespace OP.BlockSand
{
    public class FloatingScore : MonoBehaviour
    {
        [Header("Score")]
        [SerializeField]
        private Transform _scoreTextTransform;

        [SerializeField]
        private TMPro.TextMeshPro _scoreText;

        [Header("Combo")]
        [SerializeField]
        private Transform _comboNumberContainerTransform;

        [SerializeField]
        private TMPro.TextMeshPro _comboNumberText;

        [SerializeField]
        private Transform _comboTextTransform;

        [SerializeField]
        private TMP_Text _comboText;

        [SerializeField]
        private ParticleSystem _comboNumberParticle;

        [Header("Animation")]
        [SerializeField]
        private AnimationCurve _textScaleInCurve;

        [SerializeField]
        private AnimationCurve _textScaleOutCurve;

        [Header("Highlight Text")]
        [SerializeField]
        private SpriteRenderer _highlightTextRenderer;

        [SerializeField]
        private Transform _highlightTextTransform;

        [SerializeField]
        private ParticleSystem _highlightTextParticle;

        [SerializeField]
        private Sprite _goodSprite;

        [SerializeField]
        private Sprite _greatSprite;

        [SerializeField]
        private Sprite _amazingSprite;

        #region Methods

        public void Show(int score, bool isBaseScore, int currentCombo, int voiceIndex)
        {
            var targetScoreScale = isBaseScore ? 0.8f : 1f;

            _scoreTextTransform.localPosition = Vector3.zero;
            _scoreTextTransform.localScale = Vector2.zero;
            // _scoreTextTransform.localEulerAngles = isBaseScore ? Vector3.zero : new Vector3(0, 0, 7f);
            _scoreTextTransform.localEulerAngles = Vector3.zero;
            _scoreText.fontSize = isBaseScore ? 9 : 13.5f;
            _scoreText.alpha = 1;
            _comboTextTransform.localScale = Vector2.zero;
            _comboText.alpha = 1;
            _comboNumberContainerTransform.localScale = Vector2.zero;
            _comboNumberText.alpha = 1;

            _comboNumberText.text = $"{currentCombo - 1}";

            _scoreText.text = $"{score}";
            _scoreText.color = isBaseScore ? new Color(0.7f, 0.7f, 0.7f, 1f) : Color.white;
            _scoreText.enableVertexGradient = !isBaseScore;

            _highlightTextRenderer.enabled = false;

            var delayTime = 0f;

            var showingSequence = Sequence.Create();

            if (currentCombo > 1)
            {
                var durationComboText = AnimateComboText(ref showingSequence, 0f);
                var durationComboNumber = AnimateComboNumber(ref showingSequence, 0.0f);

                // delayTime += durationComboText;
            }

            AnimateScoreText(ref showingSequence, delayTime);

            // if (!isBaseScore && voiceIndex >= 0)
            //     PlayHighlightTextSound(voiceIndex);

            showingSequence.OnComplete(OnTweenFinished);

            return;

            float AnimateComboText(ref Sequence sequence, float delay)
            {
                sequence.Insert(delay + 0f, Tween.Scale(_comboTextTransform, 1f, 0.3f, _textScaleInCurve));
                // sequence.Insert(delay + 0.8f, Tween.Scale(_comboTextTransform, 0f, 0.2f, Ease.InBack));
                sequence.Insert(delay + 0.6f, Tween.Alpha(_comboText, 0f, 0.2f));
                return 1f;
            }

            float AnimateComboNumber(ref Sequence sequence, float delay)
            {
                sequence.Insert(delay + 0f, Tween.Scale(_comboNumberContainerTransform, 1f, 0.3f, _textScaleInCurve));
                sequence.InsertCallback(delay + 0f, () => { _comboNumberParticle.Play(); });
                // sequence.Insert(delay + 1.0f, Tween.Scale(_comboNumberContainerTransform, 0f, 0.3f, _textScaleOutCurve));
                sequence.Insert(delay + 0.6f, Tween.Alpha(_comboNumberText, 0f, 0.2f));
                return 1f;
            }

            void AnimateHighlightText(ref Sequence sequence, float delay)
            {
                PlayHighlightTextSound(0);

                sequence.Insert(delay + 0f, Tween.Scale(_highlightTextTransform, 1f, 0.5f, _textScaleInCurve));
                sequence.InsertCallback(delay + 0f, () => { _highlightTextParticle.Play(); });
                sequence.Insert(delay + 1.0f, Tween.Alpha(_highlightTextRenderer, 0f, 0.3f));
                // sequence.Insert(delay + 1.0f, Tween.Scale(_highlightTextTransform, 0f, 0.3f, Ease.InBack));
            }

            void AnimateScoreText(ref Sequence sequence, float delay)
            {
                sequence.Insert(delay + 0f, Tween.Scale(_scoreTextTransform, Vector2.one * targetScoreScale, 0.5f, _textScaleInCurve));
                sequence.Insert(delay + 1f, Tween.Alpha(_scoreText, 0f, 0.3f));
                // sequence.Insert(delay + 1f, Tween.Scale(_scoreTextTransform, Vector2.zero, 0.3f, _textScaleOutCurve));
            }
        }

        private void ResetToInitialState()
        {
            _scoreText.text = "";
            _scoreText.alpha = 1;

            _scoreTextTransform.localPosition = Vector3.zero;
            _scoreTextTransform.localScale = Vector2.zero;
            _comboTextTransform.localScale = Vector2.zero;
            _comboNumberContainerTransform.localScale = Vector2.zero;

            _highlightTextRenderer.color = new Color(1f, 1f, 1f, 1f);
        }

        private void OnTweenFinished()
        {
            ResetToInitialState();

            Core.ScenePool.Recycle(gameObject);
        }

        private void PlayHighlightTextSound(int voiceIndex)
        {
            switch (voiceIndex)
            {
                case 0:
                    AudioShortcut.PlayVoiceGood();
                    break;
                case 1:
                    AudioShortcut.PlayVoiceGreat();
                    break;
                case 2:
                    AudioShortcut.PlayVoiceAmazing();
                    break;
                default:
                    break;
            }
        }

        #endregion
    }
}