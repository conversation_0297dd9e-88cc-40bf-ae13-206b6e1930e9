using PrimeTween;
using TMPro;
using UnityEngine;

namespace OP.BlockSand
{
    public class ComboController : MonoBehaviour
    {
        [SerializeField]
        private Transform _comboNumberContainerTransform;

        [SerializeField]
        private TMPro.TextMeshPro _comboNumberText;

        [SerializeField]
        private Transform _comboTextTransform;

        [SerializeField]
        private TMP_Text _comboText;

        [SerializeField]
        private ParticleSystem _comboNumberParticle;

        [Header("Animation")]
        [SerializeField]
        private AnimationCurve _textScaleInCurve;

        [SerializeField]
        private AnimationCurve _textScaleOutCurve;
        
        private void Start()
        {
            _comboTextTransform.localScale = Vector2.zero;
            _comboNumberContainerTransform.localScale = Vector2.zero;
        }

        public void Show(int currentCombo)
        {
            _comboTextTransform.localScale = Vector2.zero;
            _comboText.alpha = 1;
            _comboNumberContainerTransform.localScale = Vector2.zero;
            _comboNumberText.alpha = 1;

            _comboNumberText.text = $"{currentCombo - 1}";

            var showingSequence = Sequence.Create();
            showingSequence.Insert(0f, Tween.Scale(_comboTextTransform, 1f, 0.3f, _textScaleInCurve));
            // showingSequence.Insert(0.8f, Tween.Scale(_comboTextTransform, 0f, 0.2f, Ease.InBack));
            showingSequence.Insert(1f, Tween.Alpha(_comboText, 0f, 0.2f));

            showingSequence.Insert(0f, Tween.Scale(_comboNumberContainerTransform, 1f, 0.3f, _textScaleInCurve));
            showingSequence.InsertCallback(0f, () => { _comboNumberParticle.Play(); });
            // showingSequence.Insert(1.0f, Tween.Scale(_comboNumberContainerTransform, 0f, 0.3f, _textScaleOutCurve));
            showingSequence.Insert(1f, Tween.Alpha(_comboNumberText, 0f, 0.2f));
            
            showingSequence.OnComplete(OnTweenFinished);
        }

        private void OnTweenFinished()
        {
            _comboTextTransform.localScale = Vector2.zero;
            _comboNumberContainerTransform.localScale = Vector2.zero;
        }
    }
}