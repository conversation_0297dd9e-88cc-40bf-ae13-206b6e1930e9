using System;

namespace OnePuz.Live
{
    public interface ILiveService
    {
        TimeSpan TimeLeftToRefill { get; }
        TimeSpan TimeLeftToInfiniteLive { get; }
        bool IsInfinite { get; }
        bool IsRefilling { get; }
        int CurrentLive { get; }
        bool CanPlay { get; }

        void Load();
        void EarnInfiniteLive(long durationInSeconds);
        void EarnLive(int amount = 1);
        void LostLive();
    }
}