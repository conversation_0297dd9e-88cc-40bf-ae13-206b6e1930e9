using System;
using Newtonsoft.Json;
using OnePuz.Data;

namespace OnePuz.Live
{
    public class LiveData : ISaveData, IVersionedData
    {
        [JsonProperty] public DateTime RefillEndTime { get; private set; }

        [JsonProperty] public bool IsRefilling { get; private set; }

        [JsonProperty] public bool IsInfinite { get; private set; }

        [JsonProperty] public DateTime InfiniteLiveEndTime { get; private set; }

        [JsonProperty] public int CurrentLive { get; private set; }
        
        public void SetLives(int value)
        {
            CurrentLive = Math.Clamp(value, 0, Core.Definition.Live.maxLive);
            
            Core.Event.Fire(new EventLiveChanged());
        }
        
        public void LostLives(int lives)
        {
            if (CurrentLive <= 0) return;

            CurrentLive = Math.Max(0, CurrentLive - lives);
            
            Core.Event.Fire(new EventLiveChanged());
        }

        public void Refill(long durationInSeconds)
        {
            IsRefilling = true;
            RefillEndTime = DateTime.Now.AddSeconds(durationInSeconds);
        }

        public void FinishRefill()
        {
            IsRefilling = false;
            RefillEndTime = DateTime.MinValue;
        }

        public void EarnInfiniteLive(long durationInSeconds)
        {
            IsInfinite = true;

            InfiniteLiveEndTime = InfiniteLiveEndTime == DateTime.MinValue ? DateTime.Now.AddSeconds(durationInSeconds) : InfiniteLiveEndTime.AddSeconds(durationInSeconds);

            IsRefilling = false;
            RefillEndTime = DateTime.MinValue;
        }

        public void FinishInfiniteLive()
        {
            IsInfinite = false;
            InfiniteLiveEndTime = DateTime.MinValue;
        }

        public override void SetupDefaultValues()
        {
            IsInfinite = false;
            IsRefilling = false;
            RefillEndTime = DateTime.MinValue;
        }
        
        public override void ManualSave() { }

        public int Version { get; set; } = 0;

        public void Migrate()
        {
        }
    }
}