<linker>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="_FeatureHub.UI.Utilities.SafeArea" preserve="all" />
    <type fullname="_Game.Scripts.UI.UITakeABreak" preserve="all" />
    <type fullname="OnePuz.DailyReward.UI.UIPopupDailyReward" preserve="all" />
    <type fullname="OnePuz.Live.UI.UIPopupMoreLives" preserve="all" />
    <type fullname="OnePuz.Live.UICurrency_Live" preserve="all" />
    <type fullname="OnePuz.OPBuildSettings" preserve="all" />
    <type fullname="OnePuz.OPTimeline.OPAnimatorPlayer" preserve="all" />
    <type fullname="OnePuz.Shop.UI.UIPanelShop" preserve="all" />
    <type fullname="OnePuz.Shop.UI.UIReward" preserve="all" />
    <type fullname="OnePuz.Shop.UI.UIShopItem" preserve="all" />
    <type fullname="OnePuz.UI.NiceButton" preserve="all" />
    <type fullname="OnePuz.UI.ToggleButton" preserve="all" />
    <type fullname="OnePuz.UI.UIBaseRewardTarget" preserve="all" />
    <type fullname="OnePuz.UI.UIButtonPlaySkin" preserve="all" />
    <type fullname="OnePuz.UI.UICurrency" preserve="all" />
    <type fullname="OnePuz.UI.UIObtainRewardPanel" preserve="all" />
    <type fullname="OnePuz.UI.UIPanelBannerAds" preserve="all" />
    <type fullname="OnePuz.UI.UIPanelCongratulation" preserve="all" />
    <type fullname="OnePuz.UI.UIPanelGame" preserve="all" />
    <type fullname="OnePuz.UI.UIPanelHome" preserve="all" />
    <type fullname="OnePuz.UI.UIPanelTransition" preserve="all" />
    <type fullname="OnePuz.UI.UIPanelTutorial" preserve="all" />
    <type fullname="OnePuz.UI.UIPanelWarning" preserve="all" />
    <type fullname="OnePuz.UI.UIPanelWin" preserve="all" />
    <type fullname="OnePuz.UI.UIPopupLose" preserve="all" />
    <type fullname="OnePuz.UI.UIPopupMessage" preserve="all" />
    <type fullname="OnePuz.UI.UIPopupNewBooster" preserve="all" />
    <type fullname="OnePuz.UI.UIPopupPause" preserve="all" />
    <type fullname="OnePuz.UI.UIPopupRating" preserve="all" />
    <type fullname="OnePuz.UI.UIPopupSettings" preserve="all" />
    <type fullname="OnePuz.UI.UIRewardItem" preserve="all" />
    <type fullname="OnePuz.UI.UITreasure" preserve="all" />
    <type fullname="OnePuz.Utilities.CurvedGridLayout" preserve="all" />
    <type fullname="OnePuz.Utilities.ObjectAttractor" preserve="all" />
    <type fullname="OnePuz.Utilities.ParticleAttractor" preserve="all" />
    <type fullname="OP.BlockSand.LevelInfo" preserve="all" />
    <type fullname="OP.BlockSand.PixelMaterials" preserve="all" />
    <type fullname="UIPopupRemoveAds" preserve="all" />
    <type fullname="UIPopupUpAdsRemoved" preserve="all" />
    <type fullname="OP.BlockSand.LevelPart" preserve="nothing" serialized="true" />
    <type fullname="OP.BlockSand.PixelMaterial" preserve="nothing" serialized="true" />
    <type fullname="OP.BlockSand.SceneAssetReference" preserve="nothing" serialized="true" />
    <type fullname="OnePuz.OPTimeline.ClipTransformRotation" preserve="nothing" serialized="true" />
    <type fullname="OnePuz.OPTimeline.ClipTransformScale" preserve="nothing" serialized="true" />
    <type fullname="OnePuz.OPTimeline.OPAnimatorEvents" preserve="nothing" serialized="true" />
    <type fullname="OnePuz.Shop.Definition.ShopItemData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Coffee.SoftMaskForUGUI, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Coffee.UISoftMask.MaskingShape" preserve="all" />
    <type fullname="Coffee.UISoftMask.SoftMask" preserve="all" />
    <type fullname="Coffee.UISoftMaskInternal.MinMax01" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Coffee.UIEffect, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Coffee.UIEffects.UIEffect" preserve="all" />
    <type fullname="Coffee.UIEffects.UIEffectTweener" preserve="all" />
    <type fullname="Coffee.UIEffectInternal.MinMax01" preserve="nothing" serialized="true" />
    <type fullname="Coffee.UIEffects.UIEffectTweener/TweenerEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Coffee.UIParticle, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Coffee.UIExtensions.UIParticle" preserve="all" />
  </assembly>
  <assembly fullname="ParticleImage, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AssetKits.ParticleImage.ParticleImage" preserve="all" />
    <type fullname="AssetKits.ParticleImage.Burst" preserve="nothing" serialized="true" />
    <type fullname="AssetKits.ParticleImage.Module" preserve="nothing" serialized="true" />
    <type fullname="AssetKits.ParticleImage.SeparatedMinMaxCurve" preserve="nothing" serialized="true" />
    <type fullname="AssetKits.ParticleImage.SpeedRange" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="spine-unity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Spine.Unity.SkeletonDataAsset" preserve="all" />
    <type fullname="Spine.Unity.SkeletonGraphic" preserve="all" />
    <type fullname="Spine.Unity.SkeletonSubmeshGraphic" preserve="all" />
    <type fullname="Spine.Unity.SpineAtlasAsset" preserve="all" />
    <type fullname="Spine.Unity.BlendModeMaterials" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.BlendModeMaterials/ReplacementMaterial" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.MeshGenerator" preserve="nothing" serialized="true" />
    <type fullname="Spine.Unity.MeshGenerator/Settings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
    <type fullname="UnityEngine.AddressableAssets.AssetReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.LigatureSubstitutionRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.AnimatorOverrideController" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.MonoBehaviour" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.TextAsset" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
    <type fullname="UnityEngine.ParticleSystem/MinMaxCurve" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.ParticleSystem/MinMaxGradient" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.Outline" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.Toggle" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphValueRecord" preserve="nothing" serialized="true" />
  </assembly>
</linker>