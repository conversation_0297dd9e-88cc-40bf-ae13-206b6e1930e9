{"Version": 1.0, "Metadata": {"Project": "Haptic Sampler", "Created": "5 June 2019", "Description": "A combination of transient and continuous events to create a pop with trailing sparkles."}, "Pattern": [{"Event": {"Time": 0.0, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.6}]}}, {"Event": {"Time": 0.024, "EventType": "HapticContinuous", "EventDuration": 0.15, "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.6}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.1}]}}, {"ParameterCurve": {"ParameterID": "HapticIntensityControl", "Time": 0.024, "ParameterCurveControlPoints": [{"Time": 0, "ParameterValue": 1.0}, {"Time": 0.025, "ParameterValue": 0.45}, {"Time": 0.15, "ParameterValue": 0.0}]}}, {"ParameterCurve": {"ParameterID": "HapticIntensityControl", "Time": 0.174, "ParameterCurveControlPoints": [{"Time": 0, "ParameterValue": 0.75}, {"Time": 0.087, "ParameterValue": 0.4}, {"Time": 0.667, "ParameterValue": 0.4}, {"Time": 0.846, "ParameterValue": 0.2}]}}, {"Event": {"Time": 0.174, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.4}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.188, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.6}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.203, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.231, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.261, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.278, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.9}]}}, {"Event": {"Time": 0.318, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.336, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.9}]}}, {"Event": {"Time": 0.368, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.6}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.425, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.473, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.6}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.517, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.531, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.573, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.6}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.595, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.647, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.678, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.751, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 0.841, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.8}]}}, {"Event": {"Time": 1.02, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 1.0}]}}]}