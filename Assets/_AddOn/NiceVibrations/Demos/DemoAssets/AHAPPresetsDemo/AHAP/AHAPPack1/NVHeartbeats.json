{"Version": 1.0, "Metadata": {"Project": "Haptic Sampler", "Created": "5 June 2019", "Description": "Three organic heartbeats over three seconds, made using precisely spaced transient events at varying parameters."}, "Pattern": [{"Event": {"Time": 0.0, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.2}]}}, {"Event": {"Time": 0.013, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.22, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.1}]}}, {"Event": {"Time": 0.255, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.0}]}}, {"Event": {"Time": 1.0, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.2}]}}, {"Event": {"Time": 1.013, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.22, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.1}]}}, {"Event": {"Time": 1.255, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.0}]}}, {"Event": {"Time": 2.0, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.2}]}}, {"Event": {"Time": 2.013, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.22, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.8}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.1}]}}, {"Event": {"Time": 2.255, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.0}]}}]}