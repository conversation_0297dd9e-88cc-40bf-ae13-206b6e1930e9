{"Version": 1.0, "Metadata": {"Project": "Haptic Sampler", "Created": "5 June 2019", "Description": "A sequence of tightly spaced transient events and parameter variations to create a gravel-like texture."}, "Pattern": [{"ParameterCurve": {"ParameterID": "HapticIntensityControl", "Time": 0.0, "ParameterCurveControlPoints": [{"Time": 0, "ParameterValue": 0.3}, {"Time": 1.0, "ParameterValue": 0.6}, {"Time": 3.0, "ParameterValue": 0.0}]}}, {"Event": {"Time": 0.04, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.05, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.07, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.09, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.11, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.14, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.16, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.17, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.2, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.21, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.24, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.25, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.28, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.29, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.31, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.33, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.35, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.38, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.39, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.42, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.43, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.46, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.48, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.5, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.52, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.53, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.56, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.57, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.6, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.62, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.64, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.65, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.67, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.69, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.72, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.74, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.76, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.78, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.8, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.81, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.84, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.85, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.88, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.89, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 0.92, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.94, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.96, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 0.98, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.0, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.01, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.04, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.06, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.07, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.09, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.11, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.14, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.15, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.18, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.19, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.21, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.24, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.26, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.27, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.3, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.31, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.34, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.35, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.38, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.4, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.41, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.43, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.46, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.48, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.49, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.51, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.54, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.56, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.57, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.59, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.61, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.64, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.66, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.68, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.69, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.72, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.73, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.76, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.77, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.79, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.81, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.84, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.85, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.87, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.89, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.91, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.94, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 1.95, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 1.97, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.0, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.02, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.03, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.05, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.08, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.09, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.11, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.13, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.15, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.18, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.19, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.21, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.23, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.25, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.28, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.29, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.32, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.33, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.36, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.38, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.4, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.42, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.43, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.46, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.48, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.5, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.51, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.54, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.55, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.58, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.59, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.61, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.63, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.65, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.67, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.7, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.72, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.74, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.75, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.77, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.8, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.82, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.83, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.85, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.88, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.89, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.91, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.94, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 2.95, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 1.0}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.5}]}}, {"Event": {"Time": 2.98, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}, {"Event": {"Time": 3.0, "EventType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EventParameters": [{"ParameterID": "HapticIntensity", "ParameterValue": 0.7}, {"ParameterID": "HapticSharpness", "ParameterValue": 0.3}]}}]}