A more user friendly doc is available at http://nice-vibrations-docs.moremountains.com/
Find out more about the asset at http://nice-vibrations.moremountains.com/

Nice Vibrations v3.9

## WHAT'S IN THE ASSET ? 
-------------------------

The asset contains three main folders : Common, Demos, and ThirdParty.

- As the name implies, <PERSON> contains all the scripts necessary for the mobile vibrations and gamepad rumble to work. 
You'll want to keep that folder in your game. 

- Demos contains a demo scene meant to be compiled for Android or iOS.
It's recommended to keep that folder, but if you really want to, you can remove it safely. 

- ThirdParty contains an open source json parsing library used to convert AHAP files to Android Waveforms.
You'll also want to keep that folder.

- OlderVersions contains old releases of Nice Vibrations


## HOW DO I ADD THIS TO MY GAME ?
------------------------------

You should probably go check out http://nice-vibrations-docs.moremountains.com/, there'll be more details. 
But basically all you need is to import the asset into your project, and you can instantly call the vibration 
methods from anywhere in your code. 
No need to add anything to your scenes. 
Nice Vibrations comes with universal methods (that will target both iOS and Android in one line, as well as gamepad rumble on PC/Console), 
or specific ones to get more tailored results on each platform.

## IS THERE DOCUMENTATION SOMEWHERE ?
-------------------------------------

There is!
There's a functional documentation at http://nice-vibrations-docs.moremountains.com/
And a complete API documentation at http://nice-vibrations-docs.moremountains.com/API/

## I STILL HAVE A QUESTION!
---------------------------

If something's still not clear, you can always drop me a line using the form at http://nice-vibrations.moremountains.com/. 
It's entirely possible that I forgot to document something, but please make sure you've read the documentation before filling this form. 
You can also please check the FAQ before sending me an email. Chances are, your question's answered right there. 
If it's not, then go ahead!

Also, if you're asking for support, please send me your invoice number, 
along with your Unity version and the version of Nice Vibrations you're using, so I can help you best.
