// Toony Colors Pro+Mobile 2
// (c) 2014-2022 <PERSON>

// Shader Generator Module: Reflection
// NOTE: 'REFLECTION_SHADER_FEATURE' has to be defined in the Template file

#FEATURES
sngl	lbl="Planar Reflections"		kw=PLANAR_REFLECTION												tt="Enables planar reflection, with the use of the 'TCP2_PlanarReflection' script on the reflective mesh"
sngl	lbl="Reflection Cubemap"		kw=REFLECTION_CUBEMAP												tt="Enables cubemap reflection"
sngl	lbl="Reflection Roughness"		kw=REFL_ROUGH				needs=REFLECTION_CUBEMAP	indent		tt="Simulates reflection smoothness/roughness using the cubemap's mipmap levels\n\nMake sure to enable mipmaps and specular convolution for the cubemap texture!"
sngl	lbl="Fresnel Reflections"		kw=REFLECTION_FRESNEL		needsOr=PLANAR_REFLECTION,REFLECTION_CUBEMAP,GLOSSY_REFLECTIONS	tt="Makes the reflections be more intense at grazing angles"
#END

//================================================================

#PROPERTIES_NEW
/// IF REFLECTION_CUBEMAP
		header		Reflection
		color		Reflection Cubemap Color		lighting, imp(color, label = "Color", default = (1, 1, 1, 1))
	/// IF REFL_ROUGH
		float		Reflection Cubemap Roughness	lighting, imp(range, label = "Cubemap Roughness", default = 0.5, min = 0, max = 1)
	///
///
/// IF PLANAR_REFLECTION && BUMP
		float		Planar Normals Influence	lighting, imp(range, label = "Reflection Normal Influence", default = 0.1, min = 0, max = 1)
///
/// IF REFLECTION_FRESNEL && HAS_REFLECTIONS
		float		Fresnel Min			lighting, imp(range, label = "Fresnel Min", default = 0.0, min = 0, max = 2)
		float		Fresnel Max			lighting, imp(range, label = "Fresnel Max", default = 1.5, min = 0, max = 2)
///
#END

//================================================================

#KEYWORDS
/// IF PLANAR_REFLECTION || REFLECTION_CUBEMAP || GLOSSY_REFLECTIONS
	feature_on		HAS_REFLECTIONS
///
/// IF REFLECTION_CUBEMAP
	feature_on		USE_WORLD_NORMAL_FRAGMENT
	feature_on		USE_VIEW_DIRECTION_FRAGMENT
///
/// IF REFLECTION_FRESNEL && HAS_REFLECTIONS
	feature_on		USE_NDV_FRAGMENT
///
/// IF PLANAR_REFLECTION
	feature_on		USE_SCREEN_POSITION_FRAGMENT
///
#END

//================================================================

#PROPERTIES_BLOCK
/// IF REFLECTION_CUBEMAP

		[NoScaleOffset] _Cube ("Reflection Cubemap", Cube) = "black" {}
		[[PROP:Reflection Cubemap Color]]
	/// IF REFL_ROUGH
		[[PROP:Reflection Cubemap Roughness]]
	///
///
/// IF PLANAR_REFLECTION
	/// IF BUMP
		[[PROP:Planar Normals Influence]]
	///
		[HideInInspector] _ReflectionTex ("Planar Reflection RenderTexture", 2D) = "white" {}
///
/// IF REFLECTION_FRESNEL && HAS_REFLECTIONS
		[[PROP:Fresnel Min]]
		[[PROP:Fresnel Max]]
///
#END

//================================================================

#VARIABLES
/// IF REFLECTION_CUBEMAP
	samplerCUBE _Cube;
///
/// IF PLANAR_REFLECTION
	sampler2D _ReflectionTex;
///
#END

//================================================================

#INPUT
#END

//================================================================

#VERTEX
#END

//================================================================

#LIGHTING(float3 reflectionColor, float3 normal, float3 viewDir, float ndv, float4 screenPosition, float3 normalTangentSpace)
/// IF (REFLECTION_CUBEMAP || PLANAR_REFLECTION) && REFLECTION_SHADER_FEATURE
		#if defined(TCP2_REFLECTIONS)
///
/// IF REFLECTION_CUBEMAP
		// Reflection cubemap
	/// IF !(LWRP && GLOSSY_REFLECTIONS)
		half3 reflectVector = reflect(-viewDir, normal);
	///
	/// IF REFL_ROUGH
		reflectionColor.rgb += texCUBElod(_Cube, half4(reflectVector.xyz, [[VALUE:Reflection Cubemap Roughness]] * 10.0)).rgb;
	/// ELSE
		reflectionColor.rgb += texCUBE(_Cube, reflectVector).rgb;
	///
		reflectionColor.rgb *= [[VALUE:Reflection Cubemap Color]];
///
/// IF PLANAR_REFLECTION
	/// IF BUMP
		/// IF BUMP_SHADER_FEATURE
		#if defined(_NORMALMAP)
		///
		float2 planarReflectionCoords = (screenPosition.xy + (normalTangentSpace.xy * [[VALUE:Planar Normals Influence]])) / screenPosition.w;
		reflectionColor.rgb += tex2D(_ReflectionTex, planarReflectionCoords).rgb;
		/// IF BUMP_SHADER_FEATURE
		#else
		///
	///
	/// IF !BUMP || (BUMP && BUMP_SHADER_FEATURE)
		reflectionColor.rgb += tex2D(_ReflectionTex, screenPosition.xy / screenPosition.w).rgb;
		/// IF BUMP_SHADER_FEATURE
		#endif
		///
	///
///
/// IF (REFLECTION_CUBEMAP || PLANAR_REFLECTION) && REFLECTION_SHADER_FEATURE
		#endif
///
/// IF REFLECTION_FRESNEL && HAS_REFLECTIONS
		half fresnelMin = [[VALUE:Fresnel Min]];
		half fresnelMax = [[VALUE:Fresnel Max]];
		half fresnelTerm = smoothstep(fresnelMin, fresnelMax, 1 - ndv);
		reflectionColor *= fresnelTerm;
///
#END
