using System.Collections.Generic;
using OnePuz.Definition;

namespace OnePuz.UI
{
    public class PanelArgs
    {
        #region Pool

        private static readonly Stack<PanelArgs> _pool = new Stack<PanelArgs>();
        private static readonly object _poolLock = new object();

        private static PanelArgs Get()
        {
            lock (_poolLock)
            {
                if (_pool.Count > 0)
                {
                    return _pool.Pop();
                }
            }
            return new PanelArgs();
        }

        public void Release()
        {
            mode = ShowMode.PERSISTENT;
            enableShadow = false;
            shadowAlpha = 0.65f;
            
            if (data != null)
                data.Clear();
            else
                data = new Dictionary<string, object>();
                
            lock (_poolLock)
            {
                _pool.Push(this);
            }
        }

        #endregion

        public static PanelArgs Default => Get()
            .WithMode(ShowMode.PERSISTENT)
            .EnableShadow(false);

        public static PanelArgs Popup => Get()
            .WithMode(ShowMode.NON_PERSISTENT)
            .EnableShadow(true)
            .WithShadowAlpha(0.85f);

        public PanelArgs WithMode(ShowMode mode)
        {
            this.mode = mode;
            return this;
        }

        public PanelArgs EnableShadow(bool enableShadow)
        {
            this.enableShadow = enableShadow;
            return this;
        }

        public PanelArgs WithShadowAlpha(float shadowAlpha)
        {
            this.shadowAlpha = shadowAlpha;
            return this;
        }

        public ShowMode mode;
        public bool enableShadow;
        public float shadowAlpha = 0.85f;
        public Dictionary<string, object> data = new();

        public bool TryGetData<T>(string key, out T value)
        {
            if (data == null)
            {
                value = default;
                return false;
            }

            if (data.TryGetValue(key, out var finalValue))
            {
                value = (T)finalValue;
                return true;
            }

            value = default;
            return false;
        }

        public T GetData<T>(string key)
        {
            if (data == null)
                return default;

            if (data.TryGetValue(key, out var value))
            {
                return (T)value;
            }

            return default;
        }

        public PanelArgs AddData<T>(string key, T value)
        {
            data ??= new Dictionary<string, object>();

            data.TryAdd(key, value);
            return this;
        }
    }
}