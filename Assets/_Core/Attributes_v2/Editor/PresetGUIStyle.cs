using System.Text;
using UnityEditor;
using UnityEngine;

namespace _FeatureHub.Attributes.Editor
{
    public static class PresetGUIStyle
    {
        public static readonly GUIStyle subtitle;
        public static readonly GUIStyle underline;
        public static readonly GUIStyle errorPopup;
        public static readonly GUIStyle inlineBox;
        public static readonly GUIStyle simpleButton;

        static PresetGUIStyle()
        {
            subtitle = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.MiddleLeft,
                fontStyle = FontStyle.Italic,
                fontSize = 9,
                normal = { textColor = Color.gray },
                wordWrap = true,
                richText = true
            };
            
            underline = new GUIStyle(GUI.skin.label)
            {
                alignment = TextAnchor.MiddleLeft,
                normal = { textColor = Color.gray },
            };

            errorPopup = new GUIStyle(EditorStyles.popup)
            {
                normal = { textColor = Color.red },
                hover = { textColor = Color.red },
                alignment = TextAnchor.MiddleLeft,
                richText = true
            };

            inlineBox = new GUIStyle(GUI.skin.textArea)
            {
                padding = new RectOffset
                {
                    left = GUI.skin.textArea.padding.left + 12,
                    right = GUI.skin.textArea.padding.right + 2
                }
            };

            simpleButton = new GUIStyle(EditorStyles.iconButton);
        }
    }

    public static class PresetGUIColor
    {
        public static readonly Color lightBlue;
        public static readonly Color lightGreen;
        public static readonly Color lightOrange;
        public static readonly Color lightMagenta;
        public static readonly Color lightPurple;
        public static readonly Color lightViolet;
        
        public static readonly Color orange;
        public static readonly Color blue;
        public static readonly Color purple;
        public static readonly Color violet;
        
        public static readonly Color darkWhite;
        public static readonly Color darkBlue;

        static PresetGUIColor()
        {
            lightBlue = new Color(0, 0.5f, 1) * 1.8f;
            lightGreen = Color.green * 1.5f;
            lightOrange = new Color(1, 0.5f, 0) * 1.8f;
            lightMagenta = Color.magenta * 1.8f;
            lightPurple = new Color(0.5f, 0f, 0.5f) * 1.8f;
            lightViolet = new Color(0.56f, 0f, 1f) * 1.8f;
   
            blue = new Color(0, 0.5f, 1);
            orange = new Color(1, 0.5f, 0);
            purple = new Color(0.5f, 0f, 0.5f);
            violet = new Color(0.56f, 0f, 1f);
            
            darkBlue = new Color(0, 0.5f, 1) * 0.8f;
            
            darkWhite = Color.white * 0.8f;
        }
    }
}