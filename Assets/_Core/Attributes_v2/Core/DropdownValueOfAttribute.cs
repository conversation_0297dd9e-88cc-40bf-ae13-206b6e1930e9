using System;
using System.Reflection;
using UnityEngine;

namespace _FeatureHub.Attributes.Core
{
    [AttributeUsage(AttributeTargets.Field, AllowMultiple = true)]
    public class DropDownValueOfAttribute : PropertyAttribute
    {
        public readonly bool isUniqueID;
        public readonly string[] fieldNames;
        public readonly string[] fieldValues;
        public readonly string[] fieldNameFlags;

        public DropDownValueOfAttribute(Type type, bool uniqueID = true)
        {
            isUniqueID = uniqueID;
            var fieldInfos = type.GetFields(BindingFlags.Static | BindingFlags.Public);
            fieldNames = new string[fieldInfos.Length];
            fieldValues = new string[fieldInfos.Length];
            fieldNameFlags = new string[fieldInfos.Length + 1];
            for (var i = 0; i < fieldInfos.Length; i++)
            {
                fieldNames[i] = fieldInfos[i].Name;
                fieldNameFlags[i] = fieldInfos[i].Name;
                fieldValues[i] = fieldInfos[i].GetRawConstantValue().ToString();
            }

            fieldNameFlags[^1] = "Unknown";
        }
    }
}