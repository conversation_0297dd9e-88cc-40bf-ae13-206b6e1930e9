#if UNITY_EDITOR
using UnityEditor;
#endif

using UnityEngine;
using Debug = UnityEngine.Debug;

namespace OnePuz
{
    [CreateAssetMenu(fileName = "BuildSettings.asset", menuName = "Tools/OnePuz/Build Settings")]
    public class OPBuildSettings : ScriptableObject
    {
        public string lastBuildTime;
        public string buildVersion;
        
        public string keystorePass = "123456";
        public string keyaliasPass = "123456";

#if UNITY_EDITOR

        private void OnEnable()
        {
            buildVersion = GetBuildVersion();
        }

        [MenuItem("Tools/OnePuz/Build Version")]
        public static string GetBuildVersion()
        {
            return $"v{Application.version}.{PlayerSettings.Android.bundleVersionCode}";
        }
#endif
    }
}
