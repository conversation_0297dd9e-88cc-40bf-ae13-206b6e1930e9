using System;
using MoreMountains.NiceVibrations;
using OnePuz.Data;
using UnityEngine;

namespace OnePuz.Services
{
    public interface IVibrationService
    {
        void EnableVibration(bool value);
        void Vibrate(VibrationType vibrationType = VibrationType.LIGHT);
        void VibrateLight();
        void VibrateMedium();
        void VibrateHeavy();
    }

    public enum VibrationType
    {
        LIGHT,
        MEDIUM,
        HEAVY
    }

    public class VibrationService : IVibrationService, IService
    {
        public void EnableVibration(bool value)
        {
            DataShortcut.Setting.VibrationEnabled = value;
        }

        public void Vibrate(VibrationType vibrationType = VibrationType.LIGHT)
        {
            if (Application.isEditor || !DataShortcut.Setting.VibrationEnabled) return;
            switch (vibrationType)
            {
                case VibrationType.LIGHT:
                    MMVibrationManager.Haptic(HapticTypes.LightImpact);
                    break;
                case VibrationType.MEDIUM:
                    MMVibrationManager.Haptic(HapticTypes.MediumImpact);
                    break;
                case VibrationType.HEAVY:
                    MMVibrationManager.Haptic(HapticTypes.HeavyImpact);
                    break;
                default:
                    throw new ArgumentOutOfRangeException(nameof(vibrationType), vibrationType, null);
            }
        }

        public void VibrateLight()
        {
            Vibrate(VibrationType.LIGHT);
        }

        public void VibrateMedium()
        {
            Vibrate(VibrationType.MEDIUM);
        }

        public void VibrateHeavy()
        {
            Vibrate(VibrationType.HEAVY);
        }
    }
}