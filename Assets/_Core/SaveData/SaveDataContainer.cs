using System;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json;
using OnePuz.Data.Services;
using OnePuz.Services;
using UnityEngine.Profiling;

namespace OnePuz.Data
{
    public class SaveDataContainer
    {
        private static readonly DateTimeTicksConverter DateTimeTicksConverter = new DateTimeTicksConverter();

        [ES3Serializable]
        private string _json;

        [ES3NonSerializable]
        private ISaveData _saveData;

        private bool _alreadyFetchedRemote = false;

        public SaveDataContainer()
        {
        }

        public SaveDataContainer(ISaveData saveData)
        {
            _saveData = saveData;
            _json = JsonConvert.SerializeObject(saveData, DateTimeTicksConverter);
        }

        public T GetData<T>() where T : ISaveData, new()
        {
            if (_saveData == null)
            {
                if (string.IsNullOrEmpty(_json))
                {
                    _saveData = new T();
                    _saveData.SetupDefaultValues();
                }
                else
                {
                    _saveData = JsonConvert.DeserializeObject<T>(_json, DateTimeTicksConverter);
                }
            }

            var remoteService = Core.Get<RemoteService>();
            if (!_alreadyFetchedRemote && remoteService.HasFetched)
            {
                _alreadyFetchedRemote = true;
                
                var fields = typeof(T).GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                var properties = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

                foreach (var field in fields)
                    SetRemoteValue(field, _saveData);

                foreach (var property in properties)
                    SetRemoteValue(property, _saveData);
            }

            return _saveData as T;
        }

        private void SetRemoteValue(MemberInfo member, object target)
        {
            if (member.GetCustomAttributes(typeof(RemoteValueAttribute), true).FirstOrDefault() is not RemoteValueAttribute remoteAttr)
                return;
            var memberType = member switch
            {
                FieldInfo fieldInfo => fieldInfo.FieldType,
                PropertyInfo propInfo => propInfo.PropertyType,
                _ => null
            };
            if (memberType == null)
                return;
            var method = typeof(RemoteService).GetMethod("GetValue")?.MakeGenericMethod(memberType);
            var parameters = new object[] { remoteAttr.Key, null };
            var result = method != null && (bool)method.Invoke(Core.Get<RemoteService>(), parameters);
            var remoteValue = parameters[1];

            if (!result) return;

            switch (member)
            {
                case FieldInfo field:
                    field.SetValue(target, remoteValue);
                    break;
                case PropertyInfo { CanWrite: true } prop:
                    prop.SetValue(target, remoteValue);
                    break;
            }
        }

        public void Save()
        {
            if (_saveData != null)
            {
                _saveData.ManualSave();
                _json = JsonConvert.SerializeObject(_saveData, DateTimeTicksConverter);   
            }
            // else
            //     _json = "";
        }
    }
}