using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using OnePuz.Services;
using OnePuz.Services.Extensions;
using OnePuz.TimeHandler;
using UnityEngine;

namespace OnePuz
{
    public class PerformanceService : IServiceLoad, IServiceUnload
    {
        public enum DevicePerformanceLevel
        {
            LOW,
            MEDIUM
        }
        
        public enum MemoryState
        {
            OK,
            APPROACHING_LIMIT,
            CRITICAL
        }

        private bool _isLowMemory;
        private double _lastReducePerformanceTime;
        private double _lastCheckedTargetFrameRateTime;

        private const float MEMORY_RECOVERY_TIME = 5f;

        private DevicePerformanceLevel _performanceLevel;
        public DevicePerformanceLevel Level => _performanceLevel;

        public bool IsLowMemory
        {
            get
            {
                if (_isLowMemory && (Time.realtimeSinceStartupAsDouble - _lastReducePerformanceTime > MEMORY_RECOVERY_TIME))
                {
                    _isLowMemory = false;
                }

                return _isLowMemory;
            }
        }

        private CancellationTokenSource _cancellationTokenSource = new();

        public void Load()
        {
            Application.targetFrameRate = 90;
            QualitySettings.vSyncCount = 0;
            Screen.sleepTimeout = SleepTimeout.NeverSleep;

            _isLowMemory = false;
            DetermineDevicePerformance();
            InitializeMemoryMonitoring();
            
            Screen.SetResolution(GetWidthResolution(), (int)(GetWidthResolution() / (float)Screen.width * Screen.height), true);
            
            this.EventSubscribe<TimeUpdateEvent>(HandleOnUpdate);
        }
        
        private void InitializeMemoryMonitoring()
        {
            this.EventSubscribe<UnityAppLowMemoryEvent>(HandleOnLowMemory);
        }

        private void HandleOnUpdate(TimeUpdateEvent e)
        {
            if (Time.realtimeSinceStartupAsDouble - _lastCheckedTargetFrameRateTime < 1)
            {
                return;
            }

            if (IsLowMemory) return;

            if (Application.targetFrameRate < 60)
            {
                Application.targetFrameRate = 60;
            }

            _lastCheckedTargetFrameRateTime = Time.realtimeSinceStartupAsDouble;
        }

        private void HandleOnLowMemory(UnityAppLowMemoryEvent e)
        {
            // _cancellationTokenSource = _cancellationTokenSource.RefreshToken();
            // ReducePerformanceAsync(MemoryState.APPROACHING_LIMIT, _cancellationTokenSource.Token).Forget();
        }

        private async UniTaskVoid ReducePerformanceAsync(MemoryState memoryState, CancellationToken token)
        {
            OLogger.Log($"PerformanceService ReducePerformanceAsync {memoryState.ToString()}");
            
            _isLowMemory = true;
            _lastReducePerformanceTime = Time.realtimeSinceStartupAsDouble;
            Application.targetFrameRate = 30;
            
            Core.Ads.StopBannerAutoRefresh();
            if (memoryState == MemoryState.CRITICAL)
                Core.Ads.HideBanner();
            
            await Resources.UnloadUnusedAssets().ToUniTask(cancellationToken: _cancellationTokenSource.Token);
            GC.Collect();
            Screen.SetResolution(GetWidthResolution(), (int)(GetWidthResolution() / (float)Screen.width * Screen.height), true);

            await UniTask.Delay(TimeSpan.FromSeconds(MEMORY_RECOVERY_TIME), ignoreTimeScale: true, cancellationToken: token);

            Application.targetFrameRate = 60;
            _isLowMemory = false;
            if (memoryState == MemoryState.CRITICAL
                && !DataShortcut.Ads.isAdsRemoved
                && !DataShortcut.Ads.enableAds)
                Core.Ads.ShowBanner();
            
            Core.Ads.StartBannerAutoRefresh();
            
            Screen.SetResolution(GetWidthResolution(), (int)(GetWidthResolution() / (float)Screen.width * Screen.height), true);
        }
        
        private int GetWidthResolution()
        {
            return _performanceLevel switch
            {
                DevicePerformanceLevel.LOW => 720,
                DevicePerformanceLevel.MEDIUM => 1080,
                _ => 1080
            };
        }

        private void DetermineDevicePerformance()
        {
            // Get hardware information
            var processorCount = SystemInfo.processorCount;
            var processorFrequency = SystemInfo.processorFrequency;
            var systemMemorySize = SystemInfo.systemMemorySize;
            var graphicsMemorySize = SystemInfo.graphicsMemorySize;
            var graphicsDeviceName = SystemInfo.graphicsDeviceName.ToLower();
            var deviceModel = SystemInfo.deviceModel.ToLower();
            var deviceName = SystemInfo.deviceName.ToLower();

            // Check if device is in the known low-end device list
            if (IsKnownLowEndDevice(deviceModel, deviceName))
            {
                _performanceLevel = DevicePerformanceLevel.LOW;
                OLogger.Log($"Device Performance Level: {_performanceLevel} (Known low-end device)");
                return;
            }

            // Calculate composite performance score
            var performanceScore = 0;

            // Evaluate CPU
            performanceScore += Mathf.Clamp(processorCount, 1, 8) * 10;
            performanceScore += Mathf.Clamp(Mathf.FloorToInt(processorFrequency / 500f), 1, 6) * 5;

            // Evaluate RAM
            performanceScore += Mathf.Clamp(Mathf.FloorToInt(systemMemorySize / 1000f), 1, 8) * 10;

            // Evaluate GPU
            if (graphicsMemorySize > 0)
            {
                performanceScore += Mathf.Clamp(Mathf.FloorToInt(graphicsMemorySize / 1000f), 1, 8) * 5;
            }

            // Check for low-end GPU
            if (IsLowEndGPU(graphicsDeviceName))
            {
                performanceScore = Mathf.Min(performanceScore, 60); // Cap score if GPU is weak
            }

            // Determine performance level based on score
            _performanceLevel = (performanceScore < 80)
                ? DevicePerformanceLevel.LOW
                : DevicePerformanceLevel.MEDIUM;

            OLogger.Log($"Device Performance Level: {_performanceLevel} (Score: {performanceScore})");
            OLogger.Log($"Device Info - CPU: {processorCount} cores @ {processorFrequency}MHz, RAM: {systemMemorySize}MB, GPU: {graphicsDeviceName}");
        }

        private bool IsKnownLowEndDevice(string deviceModel, string deviceName)
        {
            // List of known low-end devices
            var lowEndDevices = new string[]
            {
                "j4", "j6", "a10", "a20", "a01", "a11", "redmi 9", "redmi 8", "redmi 7",
                "y91", "y81", "y71", "moto g", "nokia 2", "nokia 3"
            };

            foreach (var device in lowEndDevices)
            {
                if (deviceModel.Contains(device) || deviceName.Contains(device))
                    return true;
            }

            return false;
        }

        private bool IsLowEndGPU(string gpuName)
        {
            // List of low-end GPUs
            var lowEndGPUs = new string[]
            {
                "adreno 505", "adreno 506", "adreno 308", "adreno 304", "adreno 306",
                "mali-t720", "mali-t820", "mali-400", "mali-g52", "mali-g51",
                "powervr ge8300", "powervr ge8100"
            };

            foreach (var gpu in lowEndGPUs)
            {
                if (gpuName.Contains(gpu))
                    return true;
            }

            return false;
        }

        public void Unload()
        {
            _cancellationTokenSource.Cancel();
            _cancellationTokenSource.Dispose();
        }
    }
}