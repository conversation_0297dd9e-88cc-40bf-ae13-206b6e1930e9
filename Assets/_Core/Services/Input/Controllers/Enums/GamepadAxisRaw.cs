// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

namespace OnePuz.InputHandler
{
	public enum GamepadAxisRaw
	{
		None = 0,
		AxisX = 1,
		AxisY = 2,
		Axis1 = AxisX,
		Axis2 = AxisY,
		Axis3 = 3,
		Axis4 = 4,
		Axis5 = 5,
		Axis6 = 6,
		Axis7 = 7,
		Axis8 = 8,
		Axis9 = 9,
		Axis10 = 10,
		Axis11 = 11,
		Axis12 = 12,
		Axis13 = 13,
		Axis14 = 14,
		Axis15 = 15,
		Axis16 = 16,
		Axis17 = 17,
		Axis18 = 18,
		Axis19 = 19,
		Axis20 = 20,
		Axis21 = 21,
		Axis22 = 22,
		Axis23 = 23,
		Axis24 = 24,
		Axis25 = 25,
		Axis<PERSON> = 26,
		<PERSON><PERSON> = 27,
		<PERSON><PERSON> = 28
	}
}
