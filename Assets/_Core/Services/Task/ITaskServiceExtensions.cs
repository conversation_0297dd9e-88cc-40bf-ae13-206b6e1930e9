// SPDX-License-Identifier: MIT
// Copyright (c) 2016-2021 <PERSON> (@JuDelCo)

using OnePuz.Handlers;
using OnePuz.Promises;
using OnePuz.Services;
using OnePuz.TimeHandler;

public static class ITaskServiceExtensions
{
	public static IPromise WaitForNextUpdate(this ITaskService taskService, ILinkHandler handle)
	{
		return taskService.WaitForSeconds<TimeUpdateEvent>(handle, 0f);
	}

	public static IPromise WaitForNextFixedUpdate(this ITaskService taskService, ILinkHandler handle)
	{
		return taskService.WaitForTicks<TimeFixedUpdateEvent>(handle, 1);
	}
}
