// Made with Amplify Shader Editor
// Available at the Unity Asset Store - http://u3d.as/y3X 
Shader "Mono/Outline/Outline Only Main 1.0"
{
	Properties
	{
		[Header(Base Color)][Space(10)]_OutlineColor("Tint", Color) = (0.1,0.05,0.15,1)
		_BaseMap("BaseMap (RGBA)", 2D) = "white" {}
		[Space(10)][Header(Lighting)][Space(10)][KeywordEnum(Unlit,Lit)] _Shading("Shading", Float) = 1
		_EmissionIntensity("Emission Intensity", Float) = 1
		[Space(10)][Header(Line)][Space(10)][KeywordEnum(World,Pixel,Hybrid)] _WidthType("Width Type", Float) = 1
		_Width("Width", Float) = 2
		[NoScaleOffset][SingleLineTexture]_WidthMask("Width Mask", 2D) = "white" {}
		[Space(10)][Header(Advanced Settings)][Space(10)]_DepthOffset("Depth Offset (Erase Detail)", Range( -1024 , 0)) = -4
		_MaxDistance("Max Distance", Range( 0 , 4)) = 3
		[Toggle]_SoftTransparency("SoftTransparency", Float) = 0
		[NoScaleOffset][SingleLineTexture][Space(10)]_MainTex("Fallback Texture", 2D) = "white" {}

	}

	SubShader
	{
		Tags { "RenderType"="Transparent" "Queue"="Transparent+1" "VRCFallback"="UnlitCutout" }
	Pass
		{
            ColorMask 0
		    Cull Off
		    ZWrite On
			Offset [_DepthOffset] , 0
		}
	Pass
		{
			Tags { "LightMode"="ForwardBase" }
		    Cull Front
		    AlphaToMask [_SoftTransparency]
		    ZWrite On
		    ZTest LEqual
			Offset 0 , 0
		    ColorMask RGBA
		    
		    Blend One Zero, One OneMinusSrcAlpha
		    
			CGPROGRAM
			#define USE_FOG 1
			#define ABSOLUTE_VERTEX_POS 1

			#pragma target 3.0 
			#pragma vertex vert
			#pragma fragment frag
			#ifdef USE_FOG
            	#pragma multi_compile_fog
			#endif
            #pragma multi_compile_fwdbase
			#include "UnityCG.cginc"
			#include "UnityShaderVariables.cginc"
			#include "Lighting.cginc"
			#include "AutoLight.cginc"
			#define ASE_NEEDS_VERT_NORMAL
			#define ASE_NEEDS_VERT_POSITION
			#define ASE_SHADOWS 1
			#define ASE_NEEDS_FRAG_WORLD_POSITION
			#pragma shader_feature_local_vertex _WIDTHTYPE_WORLD _WIDTHTYPE_PIXEL _WIDTHTYPE_HYBRID
			#pragma shader_feature_local_fragment _SHADING_UNLIT _SHADING_LIT
			#pragma multi_compile _VERTEXLIGHT_ON


			struct appdata
			{
				float4 vertex : POSITION;
            	float3 normal : NORMAL;
				float4 texcoord : TEXCOORD0;
				float4 texcoord1 : TEXCOORD1;
				UNITY_VERTEX_INPUT_INSTANCE_ID
				
			};

			struct v2f
			{
				float4 pos : SV_POSITION;
				float4 texcoord : TEXCOORD0;
				float3 worldPos : TEXCOORD2;
				#ifdef USE_FOG
                	UNITY_FOG_COORDS(3)
				#endif
				#if defined(ASE_NEEDS_FRAG_SCREEN_POSITION)
					float4 screenPos : TEXCOORD4;
				#endif
				UNITY_VERTEX_OUTPUT_STEREO
				UNITY_SHADOW_COORDS(5)
			};
			uniform float _SoftTransparency;
			uniform sampler2D _MainTex;
			uniform float _Width;
			uniform float _MaxDistance;
			uniform sampler2D _WidthMask;
			uniform float _EmissionIntensity;
			uniform float4 _OutlineColor;
			uniform sampler2D _BaseMap;
			uniform float4 _BaseMap_ST;
			float3 vertexAttenuation1_g6( inout half3 worldPos, float3 grayscaleVec, inout float4 vertexLightAtten )
			{
				 half3 col = 0;
					#ifdef  _VERTEXLIGHT_ON
				    half3 lightColor = 0;
				    half4 toLightX = unity_4LightPosX0 - worldPos.x;
				    half4 toLightY = unity_4LightPosY0 - 
				worldPos.y;
				    half4 toLightZ = unity_4LightPosZ0 - worldPos.z;
				    half4 lengthSq = 0;
				    lengthSq += toLightX * toLightX;
				    lengthSq += toLightY * toLightY;
				    lengthSq += toLightZ * toLightZ;
				    float4 atten = 1.0 / (1.0 + lengthSq * unity_4LightAtten0);
				    float4 atten2 = saturate(1 - (lengthSq * unity_4LightAtten0 / 25));
				    atten = min(atten, atten2 * atten2);
				    half4 colorFalloff = smoothstep(-0.7, 1.3, atten);
				    vertexLightAtten = atten;
				    half gs0 = dot(unity_LightColor[0], grayscaleVec);
				    half gs1 = dot(unity_LightColor[1], grayscaleVec);
				    half gs2 = dot(unity_LightColor[2], grayscaleVec);
				    half gs3 = dot(unity_LightColor[3], grayscaleVec);
				    lightColor.rgb += unity_LightColor[0]* atten.x; 
				    lightColor.rgb += unity_LightColor[1]* atten.y; 
				    lightColor.rgb += unity_LightColor[2]* atten.z; 
				    lightColor.rgb += unity_LightColor[3]* atten.w; 
				col =  lightColor;
				#endif
				return col;
			}
			


			v2f vert ( appdata v  )
			{
				v2f o;
    			UNITY_SETUP_INSTANCE_ID(v);
    			UNITY_INITIALIZE_OUTPUT(v2f, o);
    			UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);
				o.texcoord.xy = v.texcoord.xy;
				o.texcoord.zw = v.texcoord1.xy;
				float4 unityObjectToClipPos20 = UnityObjectToClipPos( v.vertex.xyz );
				float temp_output_23_0 = min( unityObjectToClipPos20.w , max( _MaxDistance , 0.01 ) );
				float temp_output_85_0 = ( _Width * 0.002 );
				#if defined(_WIDTHTYPE_WORLD)
				float staticSwitch84 = temp_output_85_0;
				#elif defined(_WIDTHTYPE_PIXEL)
				float staticSwitch84 = ( ( _Width / _ScreenParams.y ) * temp_output_23_0 );
				#elif defined(_WIDTHTYPE_HYBRID)
				float staticSwitch84 = ( temp_output_85_0 * temp_output_23_0 );
				#else
				float staticSwitch84 = ( ( _Width / _ScreenParams.y ) * temp_output_23_0 );
				#endif
				float3 ase_objectScale = float3( length( unity_ObjectToWorld[ 0 ].xyz ), length( unity_ObjectToWorld[ 1 ].xyz ), length( unity_ObjectToWorld[ 2 ].xyz ) );
				
				float3 vertexValue = float3(0, 0, 0);
				#if ABSOLUTE_VERTEX_POS
				vertexValue = v.vertex.xyz;
				#endif
				vertexValue = ( ( ( v.normal * staticSwitch84 * tex2Dlod( _WidthMask, float4( v.texcoord.xy, 0, 0.0) ).r ) / ase_objectScale ) + v.vertex.xyz );
				#if ABSOLUTE_VERTEX_POS
				v.vertex.xyz = vertexValue;
				#else
				v.vertex.xyz += vertexValue;
				#endif
				o.pos = UnityObjectToClipPos(v.vertex);
				o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
				#if ASE_SHADOWS
				TRANSFER_SHADOW( o );
				#endif
				#ifdef USE_FOG
                	UNITY_TRANSFER_FOG(o,o.pos);
				#endif
				#if defined(ASE_NEEDS_FRAG_SCREEN_POSITION)
					o.screenPos = ComputeScreenPos(o.pos);
				#endif
				return o;
			}

			fixed4 frag ( v2f i  ) : SV_Target
			{
				float3 WorldPosition = i.worldPos;
				#if defined(ASE_NEEDS_FRAG_SCREEN_POSITION)
				float4 ScreenPos = i.screenPos;
				#endif
				#if defined(LIGHTMAP_ON) && ( UNITY_VERSION < 560 || ( defined(LIGHTMAP_SHADOW_MIXING) && !defined(SHADOWS_SHADOWMASK) && defined(SHADOWS_SCREEN) ) )//aselc
				float4 ase_lightColor = 0;
				#else //aselc
				float4 ase_lightColor = _LightColor0;
				#endif //aselc
				UNITY_LIGHT_ATTENUATION(ase_atten, i, WorldPosition)
				float3 worldPos1_g6 = WorldPosition;
				float3 grayscaleVec1_g6 = float3( 0,0.1,0.23 );
				float4 vertexLightAtten1_g6 = float4( 0,0,0,0 );
				float3 localvertexAttenuation1_g6 = vertexAttenuation1_g6( worldPos1_g6 , grayscaleVec1_g6 , vertexLightAtten1_g6 );
				float3 localShadeSH955 = ShadeSH9(half4(0,1,0, 1));;
				float3 temp_cast_0 = (_EmissionIntensity).xxx;
				#if defined(_SHADING_UNLIT)
				float3 staticSwitch73 = temp_cast_0;
				#elif defined(_SHADING_LIT)
				float3 staticSwitch73 = ( saturate( ( ( ase_lightColor.rgb * ase_atten ) + localvertexAttenuation1_g6 + localShadeSH955 ) ) * _EmissionIntensity );
				#else
				float3 staticSwitch73 = ( saturate( ( ( ase_lightColor.rgb * ase_atten ) + localvertexAttenuation1_g6 + localShadeSH955 ) ) * _EmissionIntensity );
				#endif
				float2 uv_BaseMap = i.texcoord.xy * _BaseMap_ST.xy + _BaseMap_ST.zw;
				float4 temp_output_76_0 = ( _OutlineColor * tex2D( _BaseMap, uv_BaseMap ) );
				
				float3 Color = ( staticSwitch73 * (temp_output_76_0).rgb );
				float Alpha = half(1);
				fixed AlphaClipThreshold = half(.5);
				#ifdef USE_FOG
                UNITY_APPLY_FOG(i.fogCoord, Color.rgb);
				#endif
				#ifdef _ALPHATEST_ON
					clip(Alpha - AlphaClipThreshold);
				#endif
				return float4(Color,Alpha);
			}
			ENDCG
		}
    Pass
		{
            ColorMask 0
		    Cull Off
		    ZWrite On
			Offset [_DepthOffset] , 0
		}
	
	}
	Fallback "Unlit/Color"
}