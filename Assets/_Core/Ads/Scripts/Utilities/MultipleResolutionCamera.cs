using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace OnePuz
{
    public class MultipleResolutionCamera : MonoBehaviour
    {
        #region Constants
        #endregion

        #region Events
        #endregion

        #region Fields
        [SerializeField]
        private Camera m_Camera;
        #endregion

        #region Properties
        #endregion

        #region Unity Events
        private void OnValidate()
        {
            if (m_Camera == null)
                m_Camera = GetComponent<Camera>();
        }

        /*
            Some screen resolutions
            A51: 1080 x 2400        0.45
            A01 720 x 1560          0.4615
            Note 10: 2280 x 1080    0.4737    
            9 x 16                  0.5625
            10 x 16                 0.625
            3 x 4                   0.75
        */
        private void Awake()
        {
            float aspectRatio = m_Camera.aspect;
            if (aspectRatio < 9f / 16f) // Long devices
            {
                float expectedWidth = 10.5f;
                float expectedHeight = expectedWidth / aspectRatio;
                m_Camera.orthographicSize = expectedHeight / 2f;
            }
            else 
            {
                m_Camera.orthographicSize = 10f;
            }
        }
        #endregion

        #region Methods
        #endregion
    }
}
