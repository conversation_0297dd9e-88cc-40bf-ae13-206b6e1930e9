namespace OnePuz.Services
{
    public interface IAdCallbackHandler
    {
        void OnAdsClicked();
        void OnCallShowInterstitial();
        void OnClosedInterstitial();
        void OnShowInterstitialFailed();
        void OnInterstitialLoaded();
        void OnInterstitialDisplayed();
        void OnCallShowRewardedVideo();
        void OnWatchedRewardedVideo(bool succeed);
        void OnRewardedVideoLoaded();
        void OnRewardedVideoDisplayed();
        void OnBannerLoaded(bool succeed);
        void OnCallShowAppOpenAd();
        void OnAppOpenAdLoaded();
        void OnAppOpenAdDisplayed();
        void OnClosedAppOpenAd();
    }

    public interface IAdService
    {
        bool HasInitialized { get; }

        #region Delegates

        void SubscribeAdCallbacks(IAdCallbackHandler callbackHandler);

        void SubscribeRewardedVideoWatchedCallback(SimpleBoolCallback callback);
        void UnsubscribeRewardedVideoWatchedCallback(SimpleBoolCallback callback);

        void SubscribeInterstitialClosedCallback(SimpleCallback callback);
        void UnsubscribeInterstitialClosedCallback(SimpleCallback callback);

        void SubscribeAdsClickedCallback(SimpleCallback callback);
        void UnsubscribeAdsClickedCallback(SimpleCallback callback);

        #endregion

        #region Banner

        void RequestBanner(float delayTime = 0f);
        void ShowBanner();
        void HideBanner();
        void DestroyBanner();
        bool IsBannerLoaded();
        void StartBannerAutoRefresh();
        void StopBannerAutoRefresh();

        #endregion

        #region AppOpenAd

        void RequestAppOpenAd();
        void ShowAppOpenAd();
        bool IsAppOpenAdLoaded();

        #endregion

        #region Interstitial

        void RequestInterstitial();
        void ShowInterstitial();
        bool IsInterstitialLoaded();

        #endregion

        #region RewardedVideo

        void RequestRewardedVideo();
        void ShowRewardedVideo();
        bool IsRewardedVideoLoaded();

        #endregion

        #region Callbacks

        void HandleOnAdsClicked();
        void HandleOnClosedInterstitial();
        void HandleOnShowInterstitialFailed();
        void HandleOnWatchVideoReward(bool succeed);
        void HandleInterstitialLoaded();
        void HandleInterstitialDisplayed();
        void HandleRewardedVideoLoaded();
        void HandleRewardedVideoDisplayed();
        void HandleBannerLoaded(bool succeed);
        void HandleAppOpenAdLoaded();
        void HandleAppOpenAdDisplayed();
        void HandleOnClosedAppOpenAd();

        #endregion
    }
}