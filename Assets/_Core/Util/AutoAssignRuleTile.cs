using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace OnePuz.Utilities
{
#if UNITY_EDITOR
    public class AutoAssignRuleTile : EditorWindow
    {
        private Texture2D _spriteSheet;
        private string _outputFolder = "Assets/SlicedSprites";
        private RuleTile _ruleTile;

        [MenuItem("Tools/OnePuz/Auto Assign Rule Tile")]
        public static void ShowWindow()
        {
            GetWindow<AutoAssignRuleTile>("Auto Assign Rule Tile");
        }

        private void OnGUI()
        {
            GUILayout.Label("Auto Assign Rule Tile", EditorStyles.boldLabel);
            _spriteSheet = (Texture2D)EditorGUILayout.ObjectField("Sprite Sheet", _spriteSheet, typeof(Texture2D), false);
            _outputFolder = EditorGUILayout.TextField("Output Folder", _outputFolder);
            _ruleTile = (RuleTile)EditorGUILayout.ObjectField("Rule Tile", _ruleTile, typeof(RuleTile), false);

            if (!GUILayout.Button("Assign")) return;
            if (_ruleTile)
            {
                AssignSpritesToRuleTile();
            }
            else
            {
                EditorUtility.DisplayDialog("Error", "Please select a Rule Tile.", "OK");
            }
        }

        private void AssignSpritesToRuleTile()
        {
            var spriteFiles = Directory.GetFiles(_outputFolder, "*.png");
            foreach (var spriteFile in spriteFiles)
            {
                var spriteName = Path.GetFileNameWithoutExtension(spriteFile);
                var sprite = AssetDatabase.LoadAssetAtPath<Sprite>(spriteFile);
                if (!sprite) continue;
                var rule = _ruleTile.m_TilingRules.FirstOrDefault(r => r.m_Sprites.Any(s => s.name == spriteName));
                if (rule == null) continue;
                for (var i = 0; i < rule.m_Sprites.Length; i++)
                {
                    if (rule.m_Sprites[i].name == spriteName)
                    {
                        rule.m_Sprites[i] = sprite;
                    }
                }
            }

            EditorUtility.SetDirty(_ruleTile);
            AssetDatabase.SaveAssets();
            EditorUtility.DisplayDialog("Success", "Sprites assigned to Rule Tile!", "OK");
        }
    }
#endif
}