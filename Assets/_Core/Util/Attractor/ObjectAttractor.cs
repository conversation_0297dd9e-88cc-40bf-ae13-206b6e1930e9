using System;
using UnityEngine;
using System.Collections;
using Sirenix.OdinInspector;
using UnityEngine.UI;
using Cysharp.Threading.Tasks;
using UnityEngine.Events;
using UnityEngine.Serialization;

namespace OnePuz.Utilities
{
    /// <summary>
    /// A versatile component that can animate any object (UI or non-UI) to any target (UI or non-UI)
    /// using a bezier curved path.
    /// </summary>
    public class ObjectAttractor : MonoBehaviour
    {
        [<PERSON><PERSON>("Movement Settings")]
        public Transform target;
        public Transform objectToMove;

        public float duration = 2.0f;

        [Header("Path Curvature")]
        [Tooltip("Controls how much the path curves to the side")]
        [Range(0.1f, 2.0f)]
        public float curvatureFactor = 0.5f;

        [Header("Path Type")]
        [Tooltip("If true, movement will be in 3D space. If false, Z component will be ignored (2D movement in XY plane)")]
        public bool use3DPath = true;

        [ShowIf("use3DPath")]
        [Tooltip("Direction to use for curve offset in 3D mode")]
        public Vector3 curveOffsetDirection = Vector3.forward;

        [Tooltip("Controls vertical path offset. Creates dip when target is higher, arc when target is lower")]
        [Range(0.1f, 1.0f)]
        public float dropFactor = 0.7f;

        [Header("Path Visualization")]
        public bool showPath = true;

        public int pathResolution = 20;
        public Color pathColor = Color.cyan;
        public Color controlPointColor = Color.red;

        [Header("Easing")]
        public AnimationCurve easingCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);

        [Header("Callbacks")]
        [Tooltip("Event triggered when reaches the target")]
        public UnityEvent OnReachedTarget;

        private bool _isMoving = false;
        public bool IsMoving => _isMoving;

        // UI-specific variables
        private bool _isUIObject = false;
        private RectTransform _objectRectTransform;

        private void Awake()
        {
            if (objectToMove == null)
                objectToMove = transform;

            _objectRectTransform = objectToMove.GetComponent<RectTransform>();
            _isUIObject = _objectRectTransform != null;
        }

        [Button]
        public void StartMovement()
        {
            if (target == null)
            {
                OLogger.LogError("Target is not assigned!");
                return;
            }

            if (!_isMoving)
            {
                AnimateToTargetAsync(target, -1, null).Forget();
            }
        }

        /// <summary>
        /// Moves this object from a specific position to a specific target
        /// </summary>
        /// <param name="startPosition">Starting position</param>
        /// <param name="newTarget">Target transform to move to</param>
        /// <param name="customDuration">Duration in seconds</param>
        public void AnimateFromPositionToTarget(Vector3 startPosition, Transform newTarget, float customDuration)
        {
            if (newTarget == null)
            {
                OLogger.LogError("Target is not assigned!");
                return;
            }

            target = newTarget;

            if (!_isMoving)
            {
                if (objectToMove == null)
                    objectToMove = transform;

                objectToMove.position = startPosition;
                AnimateToTargetAsync(target, customDuration).Forget();
            }
        }

        /// <summary>
        /// Setup the attractor with a specific object to move and target
        /// </summary>
        public void Setup(Transform objectTransform, Transform targetTransform)
        {
            objectToMove = objectTransform;
            target = targetTransform;

            _objectRectTransform = objectToMove?.GetComponent<RectTransform>();
            _isUIObject = _objectRectTransform != null;
        }

        /// <summary>
        /// Moves the object to the target with custom settings
        /// </summary>
        /// <param name="newTarget">Target to move to</param>
        /// <param name="customDuration">Duration in seconds</param>
        /// <param name="customCamera">Camera to use for calculations</param>
        /// <returns>UniTask that completes when the movement is done</returns>
        public async UniTask AnimateToTargetAsync(Transform newTarget, float customDuration, Camera customCamera = null)
        {
            if (!newTarget)
            {
                OLogger.LogError("Target is not assigned!");
                return;
            }

            if (objectToMove == null)
            {
                OLogger.LogError("Object to move is not assigned!");
                return;
            }

            if (_isMoving) return;
            _isMoving = true;

            target = newTarget;
            customCamera ??= Camera.main;

            var moveDuration = customDuration > 0 ? customDuration : duration;
            var elapsedTime = 0f;
            var pointA = GetObjectPosition();
            var pointB = GetTargetPosition(customCamera);
            var targetLastWorldPosition = target.position;

            if (!use3DPath)
            {
                // In 2D mode, keep the same Z
                pointB.z = pointA.z;
            }

            // Store original positions to avoid changes during movement
            var originalStartPos = pointA;
            var originalTargetPos = pointB;

            // Create a cancellation token that is canceled when this component is destroyed
            var cancellationToken = this.GetCancellationTokenOnDestroy();

            try
            {
                while (elapsedTime < moveDuration)
                {
                    // Check if target was destroyed
                    if (target == null)
                    {
                        Debug.LogWarning("Target was destroyed during movement. Using last known position.");
                        break;
                    }
                    
                    if (target.position != targetLastWorldPosition)
                    {
                        targetLastWorldPosition = target.position;
                        pointB = GetTargetPosition(customCamera);
                        originalTargetPos = pointB;
                    }

                    var normalizedTime = elapsedTime / moveDuration;
                    var easedTime = easingCurve.Evaluate(normalizedTime);

                    var newPosition = BezierUtils.GetBezierPoint(
                        originalStartPos,
                        originalTargetPos,
                        curvatureFactor,
                        dropFactor,
                        use3DPath,
                        curveOffsetDirection,
                        easedTime
                    );

                    objectToMove.position = newPosition;

                    elapsedTime += Time.deltaTime;
                    await UniTask.Yield(cancellationToken);
                }

                // Ensure we end exactly at the target position
                objectToMove.position = use3DPath ? originalTargetPos : new Vector3(originalTargetPos.x, originalTargetPos.y, originalStartPos.z);
            }
            catch (OperationCanceledException)
            {
                // Operation was canceled (e.g., object was destroyed)
            }
            finally
            {
                _isMoving = false;
                if (objectToMove)
                {
                    objectToMove.position = use3DPath ? originalTargetPos : new Vector3(originalTargetPos.x, originalTargetPos.y, originalStartPos.z);
                }
                OnReachedTarget?.Invoke();
            }
        }

        /// <summary>
        /// Gets the world position of the target, handling both UI and non-UI targets
        /// </summary>
        private Vector3 GetTargetPosition(Camera mainCamera = null)
        {
            if (!target)
                return Vector3.zero;

            // Use provided camera or find main camera
            mainCamera ??= Camera.main;
            if (!mainCamera)
                return target.position;

            // Check if target is a UI element
            var targetRectTransform = target.GetComponent<RectTransform>();
            var isUITarget = targetRectTransform != null;
            // Calculate the appropriate distance from camera
            var zDistance = _isUIObject && objectToMove != null
                ? (objectToMove.position - mainCamera.transform.position).magnitude
                : 10f;

            // Case 1: Target is a UI element
            if (isUITarget)
            {
                // Get target's canvas
                var targetCanvas = target.GetComponentInParent<Canvas>();
                var objectCanvas = _isUIObject && objectToMove != null ? objectToMove.GetComponentInParent<Canvas>() : null;

                // If this object is also UI and in the same canvas, we can use the world position directly
                if (_isUIObject && objectCanvas != null && targetCanvas != null && objectCanvas == targetCanvas)
                {
                    return UIPositionConverter.GetUIElementWorldCenter(targetRectTransform);
                }

                // Convert UI position to world position
                return UIPositionConverter.UIToWorldPosition(target, mainCamera, zDistance);
            }

            // Case 2: Target is a regular Transform
            // Default case: return target position directly
            if (!_isUIObject || mainCamera == null || objectToMove == null) return target.position;
            
            // If this object is UI, we need to handle the conversion
            // Convert target world position to screen position
            var screenPos = mainCamera.WorldToScreenPoint(target.position);

            // Convert back to world position at the same distance as this UI object
            return mainCamera.ScreenToWorldPoint(new Vector3(screenPos.x, screenPos.y, zDistance));
        }

        /// <summary>
        /// Gets the world position of this object, handling both UI and non-UI objects
        /// </summary>
        private Vector3 GetObjectPosition()
        {
            if (objectToMove == null)
                return transform.position;

            // If this is not a UI object, return position directly
            if (!_isUIObject)
                return objectToMove.position;

            // For UI objects, get the center of the RectTransform
            return UIPositionConverter.GetUIElementWorldCenter(_objectRectTransform);
        }

        private void OnDrawGizmosSelected()
        {
            if (showPath && target != null)
            {
                var mainCamera = Camera.main;

                if (objectToMove == null)
                    objectToMove = transform;

                var startPos = GetObjectPosition();
                var endPos = GetTargetPosition(mainCamera);

                if (!use3DPath)
                {
                    // In 2D mode, keep the same Z for visualization
                    endPos.z = startPos.z;
                }

                BezierUtils.DrawBezierPath(
                    startPos,
                    endPos,
                    curvatureFactor,
                    dropFactor,
                    use3DPath,
                    curveOffsetDirection,
                    pathResolution,
                    pathColor,
                    controlPointColor
                );
            }
        }
    }
}