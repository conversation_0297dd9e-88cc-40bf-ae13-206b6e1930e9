using System;
using System.Collections.Generic;
using System.Globalization;
using UnityEngine;

namespace OnePuz.Extensions
{
    public static class NumberExtensions
    {
        // A list of suffixes for large numbers. You can extend this list.
    private static readonly List<string> suffixes = new List<string> { "", "K", "M", "B", "T", "q", "Q", "s", "S" };

    /// <summary>
    /// Converts a large number into a formatted string with abbreviated suffixes (K, M, B, etc.).
    /// </summary>
    /// <param name="number">The number to convert.</param>
    /// <param name="maxDigits">The maximum number of digits to display when using a suffix.</param>
    /// <param name="useSeparator">True to use thousand separators for numbers displayed in full.</param>
    /// <param name="noSuffixBelowDigits">If the number has this many digits or fewer, it will be displayed fully without a suffix.</param>
    /// <returns>The formatted string.</returns>
    public static string ToFormattedString(this double number, int maxDigits = 3, bool useSeparator = false, int noSuffixBelowDigits = 4)
    {
        if (double.IsInfinity(number) || double.IsNaN(number))
        {
            return "0";
        }

        if (number == 0)
        {
            return "0";
        }
        
        // Calculate the threshold. For example, noSuffixBelowDigits = 7 means any number < 1,000,000 will be formatted fully.
        double threshold = Mathf.Pow(10, noSuffixBelowDigits - 1);

        // If the number is below the threshold, format it as a whole number.
        if (Math.Abs(number) < threshold)
        {
            // "N0" format adds thousand separators. "F0" does not.
            string formatString = useSeparator ? "N0" : "F0";
            return number.ToString(formatString, CultureInfo.InvariantCulture);
        }

        // --- Suffixing logic for large numbers ---
        int suffixIndex = 0;
        double value = number;

        // Determine the correct suffix by repeatedly dividing by 1000.
        while (value >= 1000 && suffixIndex < suffixes.Count - 1)
        {
            value /= 1000;
            suffixIndex++;
        }

        string format;
        if (value < 10)         // e.g., 9.9K
        {
            format = "0.##";
        }
        else if (value < 100)   // e.g., 99.9K
        {
            format = "0.#";
        }
        else                    // e.g., 999K
        {
            format = "0";
        }
        
        // Format the number and apply the suffix.
        string formattedValue = value.ToString(format, CultureInfo.InvariantCulture);

        // Trim the string if it exceeds the maximum number of digits.
        if (formattedValue.Length > maxDigits)
        {
            formattedValue = formattedValue.Substring(0, maxDigits);
            // Clean up by removing a trailing decimal point.
            if (formattedValue.EndsWith("."))
            {
                formattedValue = formattedValue.Substring(0, formattedValue.Length - 1);
            }
        }

        return formattedValue + suffixes[suffixIndex];
    }

    // Overloads for other common numeric types.
    public static string ToFormattedString(this int number, int maxDigits = 3, bool useSeparator = false, int noSuffixBelowDigits = 4)
    {
        return ((double)number).ToFormattedString(maxDigits, useSeparator, noSuffixBelowDigits);
    }

    public static string ToFormattedString(this long number, int maxDigits = 3, bool useSeparator = false, int noSuffixBelowDigits = 4)
    {
        return ((double)number).ToFormattedString(maxDigits, useSeparator, noSuffixBelowDigits);
    }

    public static string ToFormattedString(this float number, int maxDigits = 3, bool useSeparator = false, int noSuffixBelowDigits = 4)
    {
        return ((double)number).ToFormattedString(maxDigits, useSeparator, noSuffixBelowDigits);
    }
    }
}